package viclass.editor.geo.impl.constructor.line

import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementLinePath
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithCoefficientTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.constructor.Intersections
import viclass.editor.geo.impl.transformer.PointOnLineWithCoefficientTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.reflect.KClass

@Singleton
class ParallelWithOtherEC : ElementConstructor<LineVi> {
    override fun outputType(): KClass<LineVi> {
        return LineVi::class
    }

    private enum class CGS {
        ThroughPointParallelWithLine, ThroughPointSegmentParallelWithLine, ThroughPointSegmentParallelWithLineAndIntersectionLine,
        ThroughPointParallelIntersectCircle, ThroughPointParallelIntersectEllipse, ThroughPointParallelIntersectSector,
        ThroughPointParallelWithCircle, ThroughPointParallelWithEllipse, ThroughPointParallelWithSector
    }

    override fun template(): ConstructorTemplate {
        val cg1 = ConstraintGroupBuilder.create()
            .name(CGS.ThroughPointParallelWithLine.name)
            .hints("LineThroughAPointParallelWithLine")
            .constraint(
                0,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfPoint"),
                "tpl-ThroughPoint"
            )
            .constraint(
                1,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aLine]!!,
                listOf("NameOfLine"),
                "tpl-ParallelWith"
            )
            .build()

        val cg2 = ConstraintGroupBuilder.create()
            .name(CGS.ThroughPointParallelIntersectCircle.name)
            .hints("LineThroughAPointParallelWithCircle")
            .constraint(
                0,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aLine]!!,
                listOf("NameOfLine"),
                "tpl-FirstLine"
            )
            .constraint(
                1,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfPoint"),
                "tpl-ThroughPoint"
            )
            .constraint(
                2,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aCircle]!!,
                listOf("NameOfCircle"),
                "tpl-ParallelWith"
            )
            .constraintOptional(
                3,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aValue]!!,
                listOf(0, 1, 2),
                listOf("IntersectionOrder"),
                "tpl-IntersectionOrder"
            )
            .build()

        val cg3 = ConstraintGroupBuilder.create()
            .name(CGS.ThroughPointParallelIntersectEllipse.name)
            .hints("LineThroughAPointParallelWithEllipse")
            .constraint(
                0,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aLine]!!,
                listOf("NameOfLine"),
                "tpl-FirstLine"
            )
            .constraint(
                1,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfPoint"),
                "tpl-ThroughPoint"
            )
            .constraint(
                2,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.anEllipse]!!,
                listOf("NameOfEllipse"),
                "tpl-ParallelWith"
            )
            .constraintOptional(
                3,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aValue]!!,
                listOf(0, 1, 2),
                listOf("IntersectionOrder"),
                "tpl-IntersectionOrder"
            )
            .build()

        val cg4 = ConstraintGroupBuilder.create()
            .name(CGS.ThroughPointParallelIntersectSector.name)
            .hints("LineThroughAPointParallelWithSector")
            .constraint(
                0,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aLine]!!,
                listOf("NameOfLine"),
                "tpl-FirstLine"
            )
            .constraint(
                1,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfPoint"),
                "tpl-ThroughPoint"
            )
            .constraint(
                2,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aCircularSector]!!,
                listOf("NameOfSector"),
                "tpl-ParallelWith"
            )
            .constraintOptional(
                3,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aValue]!!,
                listOf(0, 1, 2),
                listOf("IntersectionOrder"),
                "tpl-IntersectionOrder"
            )
            .build()

        val cg5 = ConstraintGroupBuilder.create()
            .name(CGS.ThroughPointParallelWithCircle.name)
            .hints("LineThroughAPointParallelWithCircle")
            .constraint(
                0,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfPoint"),
                "tpl-ThroughPoint"
            )
            .constraint(
                1,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aCircle]!!,
                listOf("NameOfCircle"),
                "tpl-ParallelWith"
            )
            .constraint(
                2,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfTangentPoint"),
                "tpl-TangentPoint"
            )
            .build()

        val cg6 = ConstraintGroupBuilder.create()
            .name(CGS.ThroughPointParallelWithEllipse.name)
            .hints("LineThroughAPointParallelWithEllipse")
            .constraint(
                0,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfPoint"),
                "tpl-ThroughPoint"
            )
            .constraint(
                1,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.anEllipse]!!,
                listOf("NameOfEllipse"),
                "tpl-ParallelWith"
            )
            .constraint(
                2,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfTangentPoint"),
                "tpl-TangentPoint"
            )
            .build()

        val cg7 = ConstraintGroupBuilder.create()
            .name(CGS.ThroughPointParallelWithSector.name)
            .hints("LineThroughAPointParallelWithSector")
            .constraint(
                0,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfPoint"),
                "tpl-ThroughPoint"
            )
            .constraint(
                1,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aCircularSector]!!,
                listOf("NameOfSector"),
                "tpl-ParallelWith"
            )
            .constraint(
                2,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfTangentPoint"),
                "tpl-TangentPoint"
            )
            .build()

        return ConstructorTemplateBuilder.create(this)
            .cgs(cg1, cg2, cg3, cg4, cg5, cg6, cg7)
            .elTypes(LineVi::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.ThroughPointParallelWithLine -> {
                Validations.validateNumConstraints(c, 2)
                constructParallelWithLine(doc, inputName, c)
            }

            CGS.ThroughPointSegmentParallelWithLine -> {
                Validations.validateNumConstraints(c, 3)
                constructSegmentParallelWithLine(doc, inputName, c)
            }

            CGS.ThroughPointSegmentParallelWithLineAndIntersectionLine -> {
                Validations.validateNumConstraints(c, 3)
                constructSegmentParallelWithLineAndIntersectionLine(doc, inputName, c)
            }

            CGS.ThroughPointParallelIntersectCircle -> {
                Validations.validateNumConstraints(c, 3, "min") // 3 required + optional intersection order
                constructParallelIntersectWithCurvedElement(doc, inputName, c)
            }

            CGS.ThroughPointParallelIntersectEllipse -> {
                Validations.validateNumConstraints(c, 3, "min") // 3 required + optional intersection order
                constructParallelIntersectWithCurvedElement(doc, inputName, c)
            }

            CGS.ThroughPointParallelIntersectSector -> {
                Validations.validateNumConstraints(c, 3, "min") // 3 required + optional intersection order
                constructParallelIntersectWithCurvedElement(doc, inputName, c)
            }

            CGS.ThroughPointParallelWithCircle -> {
                Validations.validateNumConstraints(c, 3)
                constructParallelWithCircle(doc, inputName, c)
            }

            CGS.ThroughPointParallelWithEllipse -> {
                Validations.validateNumConstraints(c, 3)
                constructParallelWithEllipse(doc, inputName, c)
            }

            CGS.ThroughPointParallelWithSector -> {
                Validations.validateNumConstraints(c, 3)
                constructParallelWithSector(doc, inputName, c)
            }
        }
    }

    private fun findEndingPoint(
        sPointX: Double,
        sPointY: Double,
        uVectorX: Double,
        uVectorY: Double,
        k: Double
    ): List<Double> {
        val x = sPointX + uVectorX * k;
        val y = sPointY + uVectorY * k;
        return listOf(x, y);
    }

    private fun constructSegmentParallelWithLine(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        val exr1: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val exr3 = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, c.params[2], c.ctIdx)

        val sourceLine = exr1.result.result() ?: throw ElementNotExistInDocumentException("not found source line")
        val pointThrough = exr2.result.result() ?: throw ElementNotExistInDocumentException("not found point through")
        val k = exr3.result

        val pointName = inputName?.let {
            NamePattern.extractPointName(LineVi::class, inputName) - setOf(pointThrough.name)
        }?.first() ?: generatePointName(doc)

        val vu = sourceLine.parallelVector.normalize()
        val coords = this.findEndingPoint(
            pointThrough.coordinates().x,
            pointThrough.coordinates().y,
            vu.x,
            vu.y,
            k
        )
        val newPoint = PointImpl(doc, pointName, coords[0], coords[1])
        newPoint.transformer = TransformMapping.fromClazz(PointOnLineWithCoefficientTransformer::class)
        newPoint.transformData = PointOnLineWithCoefficientTransformData(
            targetParamIdx = 2,
            paramKind = ParamKind.PK_Value,
            rootPoint = pointThrough.coordinates().toArray(),
            unitVector = vu.toArray()
        )
        newPoint.movementPath = MovementLinePath(pointThrough.coordinates().toArray(), vu.toArray())

        val newLine = LineSegmentImpl(
            doc, "${pointThrough.name}${newPoint.name}", pointThrough,
            newPoint
        )

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.addDependency(newPoint, listOf(pointThrough, sourceLine), true)

        return cr
    }

    private fun constructSegmentParallelWithLineAndIntersectionLine(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        val exr1: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val exr3: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[2], c.ctIdx)

        val sourceLine = exr1.result.result() ?: throw ElementNotExistInDocumentException("not found source line")
        val pointThrough = exr2.result.result() ?: throw ElementNotExistInDocumentException("not found point through")
        val intersectionLine =
            exr3.result.result() ?: throw ElementNotExistInDocumentException("not found intersection line")

        val pointName = inputName?.let {
            NamePattern.extractPointName(LineVi::class, inputName) - setOf(pointThrough.name)
        }?.first() ?: generatePointName(doc)
        val tempLine = LineImpl(doc, null, pointThrough, sourceLine.parallelVector)
        val intersectionPoint = Intersections.of(intersectionLine, tempLine)
            ?: throw ConstructionException("Invalid line")

        val newpoint = PointImpl(doc, pointName, intersectionPoint.x, intersectionPoint.y)

        val newLine = LineSegmentImpl(
            doc, "${pointThrough.name}${newpoint.name}", pointThrough,
            newpoint
        )

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.mergeAsDependency(exr3.result)
        cr.addDependency(newpoint, listOf(pointThrough, sourceLine, intersectionLine), true)

        return cr
    }

    private fun constructParallelWithLine(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        var exr1: ElementExtraction<Point>? = null
        var exr2: ElementExtraction<LineVi>? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                ConstraintParamDefManager.aPoint -> {
                    exr1 = extractFirstPossible(doc, ParamKind.PK_Name, p, c.ctIdx)
                }

                ConstraintParamDefManager.aLine -> {
                    exr2 = extractFirstPossible(doc, ParamKind.PK_Name, p, c.ctIdx)
                }
            }
        }

        val pointThrough = exr1!!.result.result() ?: throw ElementNotExistInDocumentException("Not found through point")
        val sourceLine = exr2!!.result.result() ?: throw ElementNotExistInDocumentException("Not found line")

        val cr = ConstructionResultImpl<LineVi>()

        val lineName: String = if (inputName.isNullOrBlank()) generateLineName(doc) else inputName
        var newLine: LineVi = LineImpl(doc, lineName, pointThrough, sourceLine.parallelVector)

        NamePattern.get(LineSegment::class)!![0].find(lineName)?.let {
            val pName1 = it.groupValues[1]
            val pName2 = it.groupValues[2]

            val p1 = doc.findElementByName(pName1, Point::class, c.ctIdx)
                ?: throw ElementNotExistInDocumentException("Not found through point")
            val p2 = doc.findElementByName(pName2, Point::class, c.ctIdx)
                ?: throw ElementNotExistInDocumentException("Not found through point")

            if (pName1 != pointThrough.name && !newLine.line().contains(p1.coordinates())) {
                throw ConstructionException("point $pName1 is out of target line $lineName")
            }
            if (pName2 != pointThrough.name && !newLine.line().contains(p2.coordinates())) {
                throw ConstructionException("point $pName2 is out of target line $lineName")
            }

            newLine = LineSegmentImpl(doc, lineName, p1, p2)

            cr.addDependency(p1, emptyList(), true)
            cr.addDependency(p2, emptyList(), true)
        }

        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)

        return cr
    }

    private fun constructParallelWithCircle(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        val exr1: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<Circle> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val exr3: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[2], c.ctIdx)

        val pointThrough = exr1.result.result() ?: throw ElementNotExistInDocumentException("Not found through point")
        val circle = exr2.result.result() ?: throw ElementNotExistInDocumentException("Not found circle")
        val tangentPoint = exr3.result.result() ?: throw ElementNotExistInDocumentException("Not found tangent point")

        // Get tangent line at the specified point on the circle
        val tangentLine = Circles.tangentAt(circle, tangentPoint)
            ?: throw ConstructionException("Cannot create tangent at the specified point")

        val cr = ConstructionResultImpl<LineVi>()
        val lineName: String = if (inputName.isNullOrBlank()) generateLineName(doc) else inputName
        val newLine: LineVi = LineImpl(doc, lineName, pointThrough, tangentLine.parallelVector)

        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.mergeAsDependency(exr3.result)

        return cr
    }

    private fun constructParallelWithEllipse(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        val exr1: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<Ellipse> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val exr3: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[2], c.ctIdx)

        val pointThrough = exr1.result.result() ?: throw ElementNotExistInDocumentException("Not found through point")
        val ellipse = exr2.result.result() ?: throw ElementNotExistInDocumentException("Not found ellipse")
        val tangentPoint = exr3.result.result() ?: throw ElementNotExistInDocumentException("Not found tangent point")

        // Get tangent line at the specified point on the ellipse
        val tangentLine = Ellipses.tangentAt(ellipse, tangentPoint)
            ?: throw ConstructionException("Cannot create tangent at the specified point")

        val cr = ConstructionResultImpl<LineVi>()
        val lineName: String = if (inputName.isNullOrBlank()) generateLineName(doc) else inputName
        val newLine: LineVi = LineImpl(doc, lineName, pointThrough, tangentLine.parallelVector)

        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.mergeAsDependency(exr3.result)

        return cr
    }

    private fun constructParallelWithSector(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        val exr1: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<CircularSector> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val exr3: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[2], c.ctIdx)

        val pointThrough = exr1.result.result() ?: throw ElementNotExistInDocumentException("Not found through point")
        val sector = exr2.result.result() ?: throw ElementNotExistInDocumentException("Not found sector")
        val tangentPoint = exr3.result.result() ?: throw ElementNotExistInDocumentException("Not found tangent point")

        // Get tangent line at the specified point on the sector
        val tangentLine = CircularSectors.tangentAt(sector, tangentPoint)
            ?: throw ConstructionException("Cannot create tangent at the specified point")

        val cr = ConstructionResultImpl<LineVi>()
        val lineName: String = if (inputName.isNullOrBlank()) generateLineName(doc) else inputName
        val newLine: LineVi = LineImpl(doc, lineName, pointThrough, tangentLine.parallelVector)

        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.mergeAsDependency(exr3.result)

        return cr
    }

    private fun constructParallelIntersectWithCurvedElement(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        var baseLine: LineVi? = null
        var pointThrough: Point? = null
        var curvedElement: Any? = null
        var intersectionOrder: Int? = null
        var extractedBaseLineResult: ElementExtraction<LineVi>? = null
        var extractedPointResult: ElementExtraction<Point>? = null
        var extractedCurvedElementResult: ElementExtraction<*>? = null
        var extractedIntersectionOrderResult: NumberExtraction<Int>? = null

        c.params.forEach { param ->
            when (param.paramDef.id) {
                ConstraintParamDefManager.aLine -> {
                    extractedBaseLineResult = extractFirstPossible(doc, ParamKind.PK_Name, param, c.ctIdx)
                    baseLine = extractedBaseLineResult!!.result.result()!!
                }
                ConstraintParamDefManager.aPoint -> {
                    extractedPointResult = extractFirstPossible(doc, ParamKind.PK_Name, param, c.ctIdx)
                    pointThrough = extractedPointResult!!.result.result()!!
                }
                ConstraintParamDefManager.aCircle -> {
                    extractedCurvedElementResult = extractFirstPossible<ElementExtraction<Circle>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    curvedElement = (extractedCurvedElementResult as ElementExtraction<Circle>).result.result()!!
                }
                ConstraintParamDefManager.anEllipse -> {
                    extractedCurvedElementResult = extractFirstPossible<ElementExtraction<Ellipse>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    curvedElement = (extractedCurvedElementResult as ElementExtraction<Ellipse>).result.result()!!
                }
                ConstraintParamDefManager.aCircularSector -> {
                    extractedCurvedElementResult = extractFirstPossible<ElementExtraction<CircularSector>>(doc, ParamKind.PK_Name, param, c.ctIdx)
                    curvedElement = (extractedCurvedElementResult as ElementExtraction<CircularSector>).result.result()!!
                }
                ConstraintParamDefManager.aValue -> {
                    extractedIntersectionOrderResult = extractFirstPossible(doc, ParamKind.PK_Value, param, c.ctIdx)
                    intersectionOrder = extractedIntersectionOrderResult!!.result
                }
            }
        }

        if (baseLine == null || pointThrough == null || curvedElement == null) {
            throw ConstructionException("Missing required parameters for parallel intersection with curved element")
        }

        // Create parallel line through the point
        val parallelLine = LineImpl(doc, generateLineName(doc), pointThrough, baseLine.parallelVector)

        // Find intersections between parallel line and curved element
        val intersections = when (curvedElement) {
            is Circle -> Intersections.of(parallelLine, curvedElement)
            is Ellipse -> Intersections.of(parallelLine, curvedElement)
            is CircularSector -> Intersections.of(parallelLine, curvedElement)
            else -> throw ConstructionException("Unsupported curved element type")
        }

        if (intersections == null || intersections.isEmpty()) {
            throw ConstructionException("No intersections found between parallel line and curved element")
        }

        // Select intersection point based on order (default to first intersection)
        val selectedIntersectionVector = if (intersectionOrder != null && intersectionOrder >= 0 && intersectionOrder < intersections.size) {
            intersections[intersectionOrder]
        } else {
            intersections[0]
        }

        // Create Point from Vector3D
        val selectedIntersectionPoint = PointImpl(doc, generatePointName(doc), selectedIntersectionVector.x, selectedIntersectionVector.y)

        // Create line segment from through point to selected intersection
        val cr = ConstructionResultImpl<LineVi>()
        val lineName: String = if (inputName.isNullOrBlank()) generateLineName(doc) else inputName
        val newLineSegment: LineVi = LineSegmentImpl(doc, lineName, pointThrough, selectedIntersectionPoint)

        cr.setResult(newLineSegment)
        extractedBaseLineResult?.let { cr.mergeAsDependency(it.result) }
        extractedPointResult?.let { cr.mergeAsDependency(it.result) }
        extractedCurvedElementResult?.let { cr.mergeAsDependency(it.result) }
        extractedIntersectionOrderResult?.let {
            // Add dependencies for the intersection construction
            cr.addDependency(newLineSegment, listOfNotNull(baseLine, curvedElement, pointThrough), true)
        }

        return cr
    }
}
