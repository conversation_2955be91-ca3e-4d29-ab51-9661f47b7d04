package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Ellipse
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.PointImpl

/**
 *
 * <AUTHOR>
 */
object Ellipses {
    fun calculatePointOnEllipseWithRadian(doc: GeoDoc, name: String?, ellipse: Ellipse, alpha: Double): Point {
        val pC = PointImpl(doc, null, ellipse.center)
        val vec = ellipse.center.vectorTo(ellipse.f2.coordinates().rotate(alpha, ellipse.center))
        val line = LineImpl(doc, null, pC, vec)
        val intersections = Intersections.of(ellipse, line)!!
        val ordered = Orders.pointsOnParallelVector(vec, intersections[0], intersections[1])
        return PointImpl(doc, name, ordered[1])
    }

    fun isOnEllipse(ellipse: Ellipse, v: Vector3D): Boolean {
        TODO("not implement yet")
    }

    fun tangentAt(ellipse: Ellipse, at: Point): LineVi? {
        TODO("not implement yet")
    }

    fun tangentThroughPoint(ellipse: Ellipse, through: Point): List<LineVi>? {
        TODO("not implement yet")
    }
}
