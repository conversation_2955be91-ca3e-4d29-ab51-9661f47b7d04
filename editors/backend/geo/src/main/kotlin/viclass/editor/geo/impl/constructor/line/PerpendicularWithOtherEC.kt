package viclass.editor.geo.impl.constructor.line

import org.apache.commons.geometry.euclidean.threed.Plane
import org.apache.commons.geometry.euclidean.threed.Planes
import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementLinePath
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithCoefficientTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.transformer.PointOnLineWithCoefficientTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import java.text.DecimalFormat
import kotlin.reflect.KClass

@Singleton
class PerpendicularWithOtherEC : ElementConstructor<LineVi> {

    override fun outputType(): KClass<LineVi> {
        return LineVi::class
    }

    private enum class CGS {
        PerpendicularWithLine, PerpendicularWithLineAt, PerpendicularWithNewPoint, PerpendicularWithIntersectLine, ThoughPointPerpendicularWithLine, ThoughPointPerpendicularWithLineAt,

    }

    override fun template(): ConstructorTemplate {
        val cg1 = ConstraintGroupBuilder.create().name(CGS.ThoughPointPerpendicularWithLine.name)
            .hints("LineThroughAPointPerpendicularWithLine").constraint(
                0,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfPoint"),
                "tpl-ThroughPoint"
            ).constraint(
                1,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aLine]!!,
                listOf("NameOfLine"),
                "tpl-PerpendicularWith",
            ).build()

        val cg2 = ConstraintGroupBuilder.create().name(CGS.PerpendicularWithIntersectLine.name)
            .hints("LineThroughAPointPerpendicularWithLine").constraint(
                0,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfPoint"),
                "tpl-ThroughPoint"
            ).constraint(
                1,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aLine]!!,
                listOf("NameOfLine"),
                "tpl-PerpendicularWith",
            ).constraint(
                2,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aLine]!!,
                listOf("NameOfLine"),
                "tpl-IntersectionWithLine",
            ).build()

        return ConstructorTemplateBuilder.create(this)
            .cgs(cg1, cg2).elTypes(LineVi::class, LineSegment::class).build()
    }

    override fun construct(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.ThoughPointPerpendicularWithLine -> {
                Validations.validateNumConstraints(c, 2)
                constructPerpendicularWithLine(doc, inputName, c)
            }

            CGS.ThoughPointPerpendicularWithLineAt -> {
                Validations.validateNumConstraints(c, 3)
                constructPerpendicularWithLine(doc, inputName, c)
            }

            CGS.PerpendicularWithLine -> {
                Validations.validateNumConstraints(c, 1)
                constructPerpendicularWithLine(doc, inputName, c)
            }

            CGS.PerpendicularWithLineAt -> {
                Validations.validateNumConstraints(c, 2)
                constructPerpendicularWithLine(doc, inputName, c)
            }

            CGS.PerpendicularWithNewPoint -> {
                Validations.validateNumConstraints(c, 3)
                constructPerpendicularWithNewPoint(doc, inputName, c)
            }

            CGS.PerpendicularWithIntersectLine -> {
                Validations.validateNumConstraints(c, 3)
                constructPerpendicularWithIntersectLine(doc, inputName, c)
            }

        }
    }

    private fun findEndingPoint(
        sPointX: Double, sPointY: Double, uVectorX: Double, uVectorY: Double, k: Double
    ): List<Double> {
        val x = sPointX + uVectorX * k;
        val y = sPointY + uVectorY * k;
        return listOf(x, y);
    }

    private fun constructPerpendicularWithNewPoint(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        val exr1 = extractFirstPossible<ElementExtraction<Point>>(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2 = extractFirstPossible<ElementExtraction<LineVi>>(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val exr3 = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, c.params[2], c.ctIdx)

        val throughPoint = exr1.result.result()!!
        val line = exr2.result.result()!!
        val k = exr3.result

        val lineVector = line.parallelVector
        val perpendicularVector = Vector3D.of(-lineVector.y, lineVector.x, 0.0)

        val pointName = inputName?.let {
            NamePattern.extractPointName(LineVi::class, inputName) - setOf(throughPoint.name)
        }?.first() ?: generatePointName(doc)
        val vu = perpendicularVector.normalize()
        val coords = this.findEndingPoint(
            throughPoint.coordinates().x, throughPoint.coordinates().y, vu.x, vu.y, k
        )
        val newPoint = PointImpl(doc, pointName, coords[0], coords[1])
        newPoint.transformer = TransformMapping.fromClazz(PointOnLineWithCoefficientTransformer::class)
        newPoint.transformData = PointOnLineWithCoefficientTransformData(
            2,
            ParamKind.PK_Value,
            throughPoint.coordinates().toArray(),
            vu.toArray()
        )
        newPoint.movementPath = MovementLinePath(throughPoint.coordinates().toArray(), vu.toArray())

        val perpendicularLine = LineSegmentImpl(doc, "${throughPoint.name}${newPoint.name}", throughPoint, newPoint)

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(perpendicularLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.addDependency(newPoint, listOf(throughPoint, line), true)

        return cr
    }

    private fun constructPerpendicularWithIntersectLine(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        var throughPoint: Point? = null
        var sourceLine: LineVi? = null
        var intersectionLine: LineVi? = null
        var extractedPointResult: ElementExtraction<Point>? = null
        var extractedLineResult: ElementExtraction<LineVi>? = null
        var intersectionLineResult: ElementExtraction<LineVi>? = null

        c.params.forEach {
            when (it.paramDef.id) {
                ConstraintParamDefManager.aPoint -> {
                    extractedPointResult =
                        extractFirstPossible(doc, ParamKind.PK_Name, it, c.ctIdx)
                    throughPoint = extractedPointResult.result.result()!!
                }

                ConstraintParamDefManager.aLine -> {

                    if (sourceLine == null) {
                        extractedLineResult =
                            extractFirstPossible(doc, ParamKind.PK_Name, it, c.ctIdx)
                        sourceLine = extractedLineResult.result.result()!!

                    } else {
                        intersectionLineResult = extractFirstPossible(doc, ParamKind.PK_Name, it, c.ctIdx)
                        intersectionLine = intersectionLineResult.result.result()!!
                    }
                }
            }
        }


        val cr = ConstructionResultImpl<LineVi>()

        if (throughPoint == null || sourceLine == null || intersectionLine == null) {
            throw ConstructionException("Invalid line")
        }

        val lineVector = sourceLine.parallelVector
        val perpendicularVector = lineVector.let { Vector3D.of(-lineVector.y, it.x, 0.0) }
        val tempLine: LineVi? = perpendicularVector?.let { LineImpl(doc, null, throughPoint, it) }
        val pointName =
            inputName?.let { NamePattern.extractPointName(LineVi::class, inputName) - setOf(throughPoint.name) }
                ?.first()
                ?: generatePointName(doc)
        val intersectionPoint =
            tempLine?.let { Intersections.of(intersectionLine, it) } ?: throw ConstructionException("Invalid line")

        val newPoint = PointImpl(doc, pointName, intersectionPoint.x, intersectionPoint.y)

        val perpendicularLine =
            LineSegmentImpl(doc, "${throughPoint.name}${newPoint.name}", throughPoint, newPoint)

        cr.setResult(perpendicularLine)
        extractedPointResult?.let { cr.mergeAsDependency(it.result) }
        extractedLineResult?.let { cr.mergeAsDependency(it.result) }
        intersectionLineResult?.let { cr.mergeAsDependency(it.result) }
        cr.addDependency(newPoint, listOfNotNull(sourceLine, intersectionLine, throughPoint), true)

        return cr
    }

    private fun constructPerpendicularWithLine(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        var pointThrough: Point? = null
        var line: LineVi? = null
        var intersectionName: String? = null
        var pointIntersection: Point? = null

        c.params.forEach {
            when (it.paramDef.id) {
                ConstraintParamDefManager.aName -> {
                    val extractedPointResult =
                        extractFirstPossible<StringExtraction>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    intersectionName = extractedPointResult.result
                    intersectionName.let {
                        pointIntersection = doc.findElementByName(it, Point::class, c.ctIdx)
                    }
                }

                ConstraintParamDefManager.aPoint -> {
                    val extractedPointResult =
                        extractFirstPossible<ElementExtraction<Point>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    pointThrough = extractedPointResult.result.result()!!
                }

                ConstraintParamDefManager.aLine -> {
                    val extractedLineResult =
                        extractFirstPossible<ElementExtraction<LineVi>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    line = extractedLineResult.result.result()!!
                }
            }
        }

        var p1Name: String? = null
        var p2Name: String? = null
        var p1: Point? = null
        var p2: Point? = null

        var newLine: LineVi?

        val lineName: String = if (inputName.isNullOrBlank()) generateLineName(doc) else inputName

        NamePattern.get(LineSegment::class)!![0].find(lineName)?.let {
            p1Name = it.groupValues[1]
            p2Name = it.groupValues[2]

            p1 = doc.findElementByName(p1Name, Point::class, c.ctIdx)
            p2 = doc.findElementByName(p2Name, Point::class, c.ctIdx)
        }

        val cr = ConstructionResultImpl<LineVi>()

        pointThrough ?: throw ElementNotExistInDocumentException("not found point through")
        line ?: throw ElementNotExistInDocumentException("not found source line")

        val plan: Plane =
            Planes.fromPointAndNormal(pointThrough.coordinates(), line.parallelVector, DEFAULT_PRECISION)

        val p = plan.intersection(line.line())

        cr.addDependency(pointThrough, emptyList(), true)
        cr.addDependency(line, emptyList(), true)

        if (pointIntersection == null) {
            if (intersectionName != null) {
                pointIntersection = PointImpl(doc, intersectionName, p.x, p.y, p.z)
                cr.addDependency(pointIntersection, listOf(line, pointThrough), true)
            } else if (p1Name != null && p2Name != null) {
                if (p1 == null) {
                    p1 = PointImpl(doc, p1Name, p.x, p.y, p.z)
                } else if (p2 == null && p1.coordinates() != p) {
                    p2 = PointImpl(doc, p2Name, p.x, p.y, p.z)
                }
            }
        } else {
            cr.addDependency(pointIntersection, listOf(), true)
        }
        val coordTP = pointThrough.coordinates()
        val formatter = DecimalFormat("#.##")

        if (formatter.format(coordTP.x) != formatter.format(p.x) || formatter.format(coordTP.y) != formatter.format(p.y)) {
            newLine = if (pointIntersection != null) LineSegmentImpl(
                doc, "${pointThrough.name}${intersectionName}", pointThrough, pointIntersection
            )
            else {
                LineImpl(doc, lineName, pointThrough, p.vectorTo(pointThrough.coordinates()))
            }
        } else if (doc.numDim == 2) {
            val l = plan.intersection(
                Planes.fromPointAndNormal(
                    Vector3D.ZERO, Vector3D.of(0.0, 0.0, 1.0), DEFAULT_PRECISION
                )
            )
            newLine = LineImpl(doc, lineName, pointThrough, l.direction)
        } else {
            val r = plan.project(Vector3D.ZERO)
            newLine = LineImpl(doc, lineName, pointThrough, r.vectorTo(p))
        }

        if (p1 != null && p2 != null) {
            if (newLine.line().contains(p1.coordinates()) && newLine.line().contains(p2.coordinates())) newLine =
                LineSegmentImpl(doc, lineName, p1, p2)
            else throw ConstructionException("They are not perpendicular with other")
        }

        cr.setResult(newLine)

        p1?.let { cr.addDependency(it, emptyList(), true) }
        p2?.let { cr.addDependency(it, emptyList(), true) }

        return cr
    }
}
