---
import { getEntry } from 'astro:content';
import config from 'virtual:starlight/user-config';
import EmptyContent from './components/EmptyMarkdown.md';
import Page from './components/Page.astro';
import { generateRouteData } from './utils/route-data';
import type { StarlightDocsEntry } from './utils/routing';
import { useTranslations } from './utils/translations';
import { BuiltInDefaultLocale } from './utils/i18n';

export const prerender = true;

const { lang = BuiltInDefaultLocale.lang, dir = BuiltInDefaultLocale.dir } =
	config.defaultLocale || {};
let locale = config.defaultLocale?.locale;
if (locale === 'root') locale = undefined;

const entryMeta = { dir, lang, locale };
const t = useTranslations(locale);

const fallbackEntry: StarlightDocsEntry = {
	slug: '404',
	id: '404.md' as StarlightDocsEntry['id'],
	body: '',
	collection: 'docs',
	data: {
		title: '404',
		template: 'splash',
		editUrl: false,
		head: [],
		hero: { tagline: t('404.text'), actions: [] },
		pagefind: false,
		sidebar: { hidden: false, attrs: {} },
		draft: false,
	},
	render: async () => ({
		Content: EmptyContent,
		headings: [],
		remarkPluginFrontmatter: {},
	}),
};

const userEntry = await getEntry('docs', '404');
const entry = userEntry || fallbackEntry;
const { Content, headings } = await entry.render();
const route = generateRouteData({
	props: { ...entryMeta, entryMeta, headings, entry, id: entry.id, slug: entry.slug },
	url: Astro.url,
});
---

<Page {...route}><Content /></Page>
