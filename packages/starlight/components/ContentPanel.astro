---
import type { Props } from '../props';
---

<div class="content-panel">
	<div class="sl-container"><slot /></div>
</div>

<style>
	.content-panel {
		padding: 1.5rem var(--sl-content-pad-x);
	}
	.content-panel + .content-panel {
		border-top: 1px solid var(--sl-color-hairline);
	}
	.sl-container {
		max-width: var(--sl-content-width);
	}

	.sl-container > :global(* + *) {
		margin-top: 1.5rem;
	}

	@media (min-width: 72rem) {
		.sl-container {
			margin-inline: var(--sl-content-margin-inline, auto);
		}
	}
</style>
