.sl-markdown-content
	:not(a, strong, em, del, span, input, code)
	+ :not(a, strong, em, del, span, input, code, :where(.not-content *)) {
	margin-top: 1rem;
}

/* Headings after non-headings have more spacing. */
.sl-markdown-content
	:not(h1, h2, h3, h4, h5, h6)
	+ :is(h1, h2, h3, h4, h5, h6):not(:where(.not-content *)) {
	margin-top: 1.5em;
}

.sl-markdown-content li + li:not(:where(.not-content *)),
.sl-markdown-content dt + dt:not(:where(.not-content *)),
.sl-markdown-content dt + dd:not(:where(.not-content *)),
.sl-markdown-content dd + dd:not(:where(.not-content *)) {
	margin-top: 0.25rem;
}

.sl-markdown-content li:not(:where(.not-content *)) {
	overflow-wrap: anywhere;
}

.sl-markdown-content
	li
	> :last-child:not(li, ul, ol):not(a, strong, em, del, span, input, :where(.not-content *)) {
	margin-bottom: 1.25rem;
}

.sl-markdown-content dt:not(:where(.not-content *)) {
	font-weight: 700;
}
.sl-markdown-content dd:not(:where(.not-content *)) {
	padding-inline-start: 1rem;
}

.sl-markdown-content :is(h1, h2, h3, h4, h5, h6):not(:where(.not-content *)) {
	color: var(--sl-color-white);
	line-height: var(--sl-line-height-headings);
	font-weight: 600;
}

.sl-markdown-content :is(img, picture, video, canvas, svg, iframe):not(:where(.not-content *)) {
	display: block;
	max-width: 100%;
	height: auto;
}

.sl-markdown-content h1:not(:where(.not-content *)) {
	font-size: var(--sl-text-h1);
}
.sl-markdown-content h2:not(:where(.not-content *)) {
	font-size: var(--sl-text-h2);
}
.sl-markdown-content h3:not(:where(.not-content *)) {
	font-size: var(--sl-text-h3);
}
.sl-markdown-content h4:not(:where(.not-content *)) {
	font-size: var(--sl-text-h4);
}
.sl-markdown-content h5:not(:where(.not-content *)) {
	font-size: var(--sl-text-h5);
}
.sl-markdown-content h6:not(:where(.not-content *)) {
	font-size: var(--sl-text-h6);
}

.sl-markdown-content a:not(:where(.not-content *)) {
	color: var(--sl-color-text-accent);
}
.sl-markdown-content a:hover:not(:where(.not-content *)) {
	color: var(--sl-color-white);
}

.sl-markdown-content code:not(:where(.not-content *)) {
	background-color: var(--sl-color-bg-inline-code);
	margin-block: -0.125rem;
	padding: 0.125rem 0.375rem;
	font-size: var(--sl-text-code-sm);
}
.sl-markdown-content :is(h1, h2, h3, h4, h5, h6) code {
	font-size: inherit;
}

.sl-markdown-content pre:not(:where(.not-content *)) {
	border: 1px solid var(--sl-color-gray-5);
	padding: 0.75rem 1rem;
	font-size: var(--sl-text-code);
	tab-size: 2;
}

.sl-markdown-content pre code:not(:where(.not-content *)) {
	all: unset;
	font-family: var(--__sl-font-mono);
}

.sl-markdown-content blockquote:not(:where(.not-content *)) {
	border-inline-start: 1px solid var(--sl-color-gray-5);
	padding-inline-start: 1rem;
}

/* Table styling */
.sl-markdown-content table:not(:where(.not-content *)) {
	display: block;
	overflow: auto;
	border-spacing: 0;
}
.sl-markdown-content :is(th, td):not(:where(.not-content *)) {
	border-bottom: 1px solid var(--sl-color-gray-5);
	padding: 0.5rem 1rem;
	/* Align text to the top of the row in multiline tables. */
	vertical-align: baseline;
}
.sl-markdown-content :is(th:first-child, td:first-child):not(:where(.not-content *)) {
	padding-inline-start: 0;
}
.sl-markdown-content :is(th:last-child, td:last-child):not(:where(.not-content *)) {
	padding-inline-end: 0;
}
.sl-markdown-content th:not(:where(.not-content *)) {
	color: var(--sl-color-white);
	font-weight: 600;
}
/* Align headings to the start of the line unless set by the `align` attribute. */
.sl-markdown-content th:not([align]):not(:where(.not-content *)) {
	text-align: start;
}
/* <table>s, <hr>s, and <blockquote>s inside asides */
.sl-markdown-content .starlight-aside :is(th, td, hr, blockquote):not(:where(.not-content *)) {
	border-color: var(--sl-color-gray-4);
}
@supports (border-color: color-mix(in srgb, var(--sl-color-asides-text-accent) 30%, transparent)) {
	.sl-markdown-content .starlight-aside :is(th, td, hr, blockquote):not(:where(.not-content *)) {
		border-color: color-mix(in srgb, var(--sl-color-asides-text-accent) 30%, transparent);
	}
}

/* <code> inside asides */
@supports (border-color: color-mix(in srgb, var(--sl-color-asides-text-accent) 12%, transparent)) {
	.sl-markdown-content .starlight-aside code:not(:where(.not-content *)) {
		background-color: color-mix(in srgb, var(--sl-color-asides-text-accent) 12%, transparent);
	}
}

.sl-markdown-content hr:not(:where(.not-content *)) {
	border: 0;
	border-bottom: 1px solid var(--sl-color-hairline);
}

/* <details> and <summary> styles */
.sl-markdown-content details:not(:where(.not-content *)) {
	--sl-details-border-color: var(--sl-color-gray-5);
	--sl-details-border-color--hover: var(--sl-color-text-accent);

	border-inline-start: 2px solid var(--sl-details-border-color);
	padding-inline-start: 1rem;
}
.sl-markdown-content details:not([open]):hover:not(:where(.not-content *)),
.sl-markdown-content details:has(> summary:hover):not(:where(.not-content *)) {
	border-color: var(--sl-details-border-color--hover);
}
.sl-markdown-content summary:not(:where(.not-content *)) {
	color: var(--sl-color-white);
	cursor: pointer;
	display: block; /* Needed to hide the default marker in some browsers. */
	font-weight: 600;
	/* Expand the outline so that the marker cannot distort it. */
	margin-inline-start: -0.5rem;
	padding-inline-start: 0.5rem;
}
.sl-markdown-content details[open] > summary:not(:where(.not-content *)) {
	margin-bottom: 1rem;
}

/* <summary> marker styles */
.sl-markdown-content summary:not(:where(.not-content *))::marker,
.sl-markdown-content summary:not(:where(.not-content *))::-webkit-details-marker {
	display: none;
}
.sl-markdown-content summary:not(:where(.not-content *))::before {
	--sl-details-marker-size: 1.25rem;

	background-color: currentColor;
	content: '';
	display: inline-block;
	height: var(--sl-details-marker-size);
	width: var(--sl-details-marker-size);
	margin-inline: calc((var(--sl-details-marker-size) / 4) * -1) 0.25rem;
	vertical-align: middle;
	-webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M14.8 11.3 10.6 7a1 1 0 1 0-1.4 1.5l3.5 3.5-3.5 3.5a1 1 0 0 0 0 1.4 1 1 0 0 0 .7.3 1 1 0 0 0 .7-.3l4.2-4.2a1 1 0 0 0 0-1.4Z'/%3E%3C/svg%3E%0A");
	mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M14.8 11.3 10.6 7a1 1 0 1 0-1.4 1.5l3.5 3.5-3.5 3.5a1 1 0 0 0 0 1.4 1 1 0 0 0 .7.3 1 1 0 0 0 .7-.3l4.2-4.2a1 1 0 0 0 0-1.4Z'/%3E%3C/svg%3E%0A");
	-webkit-mask-repeat: no-repeat;
	mask-repeat: no-repeat;
}
@media (prefers-reduced-motion: no-preference) {
	.sl-markdown-content summary:not(:where(.not-content *))::before {
		transition: transform 0.2s ease-in-out;
	}
}
.sl-markdown-content details[open] > summary:not(:where(.not-content *))::before {
	transform: rotateZ(90deg);
}
[dir='rtl'] .sl-markdown-content summary:not(:where(.not-content *))::before,
.sl-markdown-content [dir='rtl'] summary:not(:where(.not-content *))::before {
	transform: rotateZ(180deg);
}
/* <summary> with only a paragraph automatically added when using MDX */
.sl-markdown-content summary:not(:where(.not-content *)) p:only-child {
	display: inline;
}

/* <details> styles inside asides */
.sl-markdown-content .starlight-aside details:not(:where(.not-content *)) {
	--sl-details-border-color: var(--sl-color-asides-border);
	--sl-details-border-color--hover: var(--sl-color-asides-text-accent);
}
