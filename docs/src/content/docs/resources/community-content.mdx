---
title: Community Content
description: Discover community-produced guides, articles and videos to help you learn and build with Starlight!
---

:::tip[Add your own!]
Have you produced content about Starlight?
Open a PR adding a link to this page!
:::

import { CardGrid, LinkCard } from '@astrojs/starlight/components';

## Articles and Reviews

Here is a collection of posts and articles to learn more about Starlight and other people's experiences:

<CardGrid>
	<LinkCard
		href="https://devm.io/open-source/starlight-astro"
		title="Static Site Generation with Starlight"
		description="“No idea is too big or too small when designing components” — an interview with <PERSON>, Starlight Lead"
	/>
	<LinkCard
		href="https://frontendatscale.com/blog/hybrid-frontend-architecture/"
		title="Hybrid Frontend Architecture with Astro and Starlight"
		description="<PERSON><PERSON> and <PERSON> build a docs site with Starlight, TinaCMS, and an interactive API Playground with authentication."
	/>
	<LinkCard
		href="https://www.olets.dev/posts/comparing-docs-site-builders-vuepress-vs-starlight/"
		title="Comparing docs site builders: Vue<PERSON>ress vs Starlight"
		description="How do these two frameworks measure up?"
	/>
</CardGrid>

## Recipes and Guides

Recipes are typically short, focused how-to guides that walk a reader through completing a working example of a specific task. Recipes are a great way to add new features or behavior to your Starlight project by following step-by-step instructions! Other guides might explain concepts related to an area of content, such as using images or working with MDX.

Explore community-produced content maintained by Starlight users:

<CardGrid>
	<LinkCard
		href="https://www.webpro.nl/scraps/versioned-docs-with-starlight-and-vercel"
		title="Versioned documentation with Starlight & Vercel"
		description="A guide to implementing separate versions of documentation for each major version of a project"
	/>
	<LinkCard
		href="https://hideoo.dev/notes/starlight-heading-links"
		title="Add links to Starlight headings"
		description="A guide to using a rehype plugin to share links to specific sections of your documentation"
	/>
	<LinkCard
		href="https://blog.otterlord.dev/posts/starlight-sponsors/"
		title="Add sponsors to your Starlight site"
		description="A guide to implement a custom sponsors component in your documentation sidebar"
	/>
	<LinkCard
		href="https://hideoo.dev/notes/starlight-og-images"
		title="Add Open Graph images to Starlight"
		description="A guide to generating social images and the corresponding meta tags for your pages"
	/>
	<LinkCard
		href="https://hideoo.dev/notes/starlight-third-party-icon-sets"
		title="Use third-party icon sets in Starlight"
		description="A guide to using unplugin-icons to expand the selection of available icons for Starlight"
	/>
	<LinkCard
		href="https://hideoo.dev/notes/starlight-custom-html-head"
		title="Edit the HTML head of Starlight pages"
		description="Learn how to add common head content such as web analytics, fonts, and scripts"
	/>
	<LinkCard
		href="https://dev.to/mrrobot/publishing-documentation-with-astro-starlight-691"
		title="Publishing Documentation with Astro Starlight"
		description="Getting started with Starlight documentation"
	/>
	<LinkCard
		href="https://events-3bg.pages.dev/jotter/starlight/guide/"
		title="Enable View Transitions"
		description="Get that SPA-like look and feel with the Bag of Tricks’ view transitions support"
	/>
	<LinkCard
		href="https://jamcomments.com/posts/structured-data-with-starlight"
		title="Adding Structured Data to Starlight Pages"
		description="Learn how to build dynamic JSON-LD structured data for your documentation pages."
	/>
	<LinkCard
		href="https://starlight-examples.netlify.app/"
		title="Starlight Examples"
		description="A collection of StackBlitz embeds demonstrating practical ways of doing stuff in Starlight documentation sites."
	/>
</CardGrid>

## Video Content

Discover videos and channels with Starlight content, including live streams and educational content.

import YouTubeGrid from '~/components/youtube-grid.astro';

### Astro Videos

<YouTubeGrid
	videos={[
		{
			href: 'https://www.youtube.com/watch?v=5u0Ds7wzUeI',
			title: 'Starlight by Astro',
			description: 'Watch the official Starlight launch video',
		},
		{
			href: 'https://www.youtube.com/shorts/zjOWezSzd18',
			title: '🌟 SUB 1 MINUTE RUN',
			description: 'Watch Ben launch a new Starlight site in under a minute!',
		},
	]}
/>

### Community Videos and Streams

<YouTubeGrid
	videos={[
		{
			href: 'https://www.youtube.com/watch?v=sF6UcV3moZg',
			title: 'Building docs with Starlight and Astro',
			description:
				'Watch Chris and Alex dive into Starlight on Code with CodingCat.dev',
		},
		{
			href: 'https://www.youtube.com/watch?v=sWkkHbwDeQc',
			title: 'Astro Starlight',
			description: 'Introduction to Starlight in under a minute.',
		},
		{
			href: 'https://www.youtube.com/watch?v=-Ki-1E5gNCk',
			title: 'Astro Starlight Documentation Template (build custom app docs!)',
			description: 'Get a new Starlight site up and running in about 5 minutes',
		},
		{
			href: 'https://www.youtube.com/watch?v=12o7WxjAxjM',
			title: 'Include Starlight docs in a Next.js project with proxies',
			description:
				'Set up Starlight as a sub-directory project inside a Next.js website',
		},
		{
			href: 'https://www.youtube.com/watch?v=5pq80drDrNs',
			title: "I Recreated Shadcn's Docs In 30 Minutes With This Amazing Tool",
			description:
				'In this video I go over what makes Starlight so cool and why you may want to give it a try on your next project.',
		},
		{
			href: 'https://www.youtube.com/watch?v=Q1E4Gkt63ko',
			title: 'Astro Starlight and emoji-blast with Chris Swithinbank',
			description:
				"Wherein Chris, the creator of Astro's Starlight framework-on-a-framework, walks me through setting it up for a new emoji-blast website. 🌟 🎇",
		},
	]}
/>
