---
title: Starlight 🌟 Создавайте сайты с документацией с помощью Astro
head:
  - tag: title
    content: Starlight 🌟 Создавайте сайты с документацией с помощью Astro
description: Starlight помогает вам создавать красивые, высокопроизводительные веб-сайты с документацией с Astro.
template: splash
editUrl: false
lastUpdated: false
hero:
  title: Сделайте свою документацию яркой с помощью Starlight
  tagline: Всё, что вам нужно, чтобы создать впечатляющий сайт с документацией. Быстро, доступно и просто в использовании.
  image:
    file: ~/assets/hero-star.webp
  actions:
    - text: Начать
      icon: right-arrow
      variant: primary
      link: /ru/getting-started/
    - text: Открыть в GitHub
      icon: external
      link: https://github.com/withastro/starlight
---

import { CardGrid, Card } from '@astrojs/starlight/components';
import AboutAstro from '~/components/about-astro.astro';
import TestimonialGrid from '~/components/testimonial-grid.astro';
import Testimonial from '~/components/testimonial.astro';

<CardGrid stagger>
	<Card title="Документация, которая восхищает" icon="open-book">
		Включает в себя: навигацию по сайту, поиск, интернационализацию, оптимизацию
		для поисковых систем (SEO), удобную типографику, подсветку кода, тёмный
		режим и многое другое.
	</Card>
	<Card title="Работает на Astro" icon="rocket">
		Воспользуйтесь всей мощью и производительностью Astro. Расширьте Starlight с
		вашими любимыми интеграциями и библиотеками Astro.
	</Card>
	<Card title="Markdown, Markdoc и MDX" icon="document">
		Используйте свой любимый язык разметки. Starlight предоставляет встроенную
		проверку метаданных с поддержкой TypeScript для обеспечения
		типобезопасности.
	</Card>
	<Card title="Используйте свои UI компоненты" icon="puzzle">
		Starlight не зависим от других фреймворков. Используйте с ним React, Vue,
		Svelte, Solid и другие фреймворки.
	</Card>
</CardGrid>

<TestimonialGrid title="Что говорят люди">
  <Testimonial
    name="Rachel"
    handle="rachelnabors"
    cite="https://twitter.com/astrodotbuild/status/1724934718745915558"
  >
    Команда Astro РАЗВИВАЛА способы создания документации, и вы можете получить всё это прямо из коробки с их проектом Starlight.
  </Testimonial>
  <Testimonial
    name="Flavio"
    handle="flaviocopes"
    cite="https://twitter.com/flaviocopes/status/1738237658717905108"
  >
    Официальный стартовый комплект Astro Starlight — поистине невероятный инструмент для создания веб-сайта документации.
  </Testimonial>
  <Testimonial
    name="Tomek"
    handle="sulco"
    cite="https://twitter.com/sulco/status/1735610348730802342"
  >
    Starlight — наш лучший пример отличного DX: скорость, удобство и
    внимание к деталям вдохновляет. Он заботится о технологиях и внешнем виде,
    чтобы вы могли сосредоточиться на своем контенте 👏

    Команде StackBlitz это очень нравится!

  </Testimonial>
  <Testimonial
    name="Roberto"
    handle="RmeetsH"
    cite="https://twitter.com/RmeetsH/status/1735783992018760090"
  >
    Starlight изменил правила игры для меня, позволив мне сосредоточиться на создании контента.

    Его интуитивно понятный дизайн не только оптимизирует мой рабочий процесс, но и сокращает время адаптации для разработчиков ПО с открытым исходным кодом.

  </Testimonial>
  <Testimonial
    name="Joel"
    handle="jhooks"
    cite="https://twitter.com/jhooks/status/1727405160547418405"
  >
    Потратил ещё немного времени на Starlight для документации Course Builder, и до сих пор всё было отлично. Множество приятных мелочей и возможность сосредоточиться на написании в Markdown, а не на возне с сайтом.
  </Testimonial>
  <Testimonial
    name="Rick"
    handle="rick_viscomi"
    cite="https://twitter.com/rick_viscomi/status/1665867447910510593"
  >
    Начал играть со Starlight. Должен сказать, что я очень впечатлен производительностью «из коробки».

    💯💯💯💯

  </Testimonial>
  <Testimonial
    name="Nicolas"
    handle="beaussan"
    cite="https://twitter.com/beaussan/status/1735625189583466893"
  >
    Starlight — лучший способ начать работу с документацией:
	мощь и скорость Astro и инструменты Starlight — это идеальное сочетание.

    Я уже давно это делаю и продолжаю любить!

  </Testimonial>
  <Testimonial
    name="Sylwia"
    handle="SylwiaVargas"
    cite="https://x.com/SylwiaVargas/status/1726556825741578286"
  >
    Я использовала Starlight на своей последней работе, и мне он понравился. Отличные компоненты, интуитивно понятный
    дизайн и сверхотзывчивое сообщество (всякий раз, когда кому-то что-то нужно,
    они очень быстро направят его в нужном направлении или подскажут обходной путь). Очень приятный опыт.
  </Testimonial>
  <Testimonial
    name="Lou Cyx"
    handle="loucyx"
    cite="https://elk.zone/m.webtoo.ls/@<EMAIL>/111587380021362284"
  >
    Документы на моем сайте монорепозитория выглядят лучше, чем когда-либо, благодаря Starlight. Его чрезвычайно легко использовать, не теряя при этом всей мощи Astro. Спасибо за работу над этим!
  </Testimonial>
  <Testimonial
    name="BowTiedWebReaper"
    handle="BowTiedWebReapr"
    cite="https://twitter.com/BowTiedWebReapr/status/1735633399501697517"
  >
    Starlight — мой любимый инструмент для документирования. Благодаря этому стало очень легко добавлять документы на мой существующий веб-сайт продукта Astro, вместо того, чтобы использовать поддомен с другим инструментом.
  </Testimonial>
  <Testimonial
    name="Jeff"
    handle="J_Everhart383"
    cite="https://twitter.com/J_Everhart383/status/1691900590048292908"
  >
    Я перестраивал документацию платформы WPEngine Atlas. Поверьте мне, когда я говорю, что у Starlight есть всё необходимое для создания платформы документации A+ 🙌
  </Testimonial>
  <Testimonial
    name="Chloe"
    handle="solelychloe"
    cite="https://twitter.com/solelychloe/status/1695115277602628082"
  >
    Попробуйте Starlight!

    Я использую его для нескольких своих сайтов, и это здорово.

  </Testimonial>
</TestimonialGrid>

<AboutAstro title="Предоставлено вам">
Astro — универсальный веб-фреймворк, разработанный для максимальной производительности.
Используйте контент из любых источников и развёртывайте его везде, используя ваши любимые UI-компоненты и библиотеки.

[Узнать больше об Astro](https://astro.build/)

</AboutAstro>
