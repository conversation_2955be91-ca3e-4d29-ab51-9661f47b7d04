---
title: Instalação Manual
description: Aprenda como instalar o Starlight manualmente e adicioná-lo a um projeto Astro existente.
---

import { Tabs, TabItem } from '@astrojs/starlight/components';

A forma mais rápida de criar um novo site Starlight é utilizando `create astro` como é mostrado no [guia <PERSON>trodu<PERSON>](/pt-br/getting-started/#crie-um-novo-projeto).
Se você deseja adicionar Starlight a um projeto Astro existente, este guia irá te explicar como.

## Instale Starlight

Para seguir este guia, você vai precisar de um projeto Astro existente.

### Adicione a integração Starlight

Starlight é uma [integração Astro](https://docs.astro.build/pt-br/guides/integrations-guide/). Adicione-o ao seu site executando o comando `astro add` no diretório raiz do seu projeto:

<Tabs>
    <TabItem label="npm">
        ```sh
        npx astro add starlight
        ```

    </TabItem>
    <TabItem label="pnpm">
        ```sh
        pnpm astro add starlight
        ```
    </TabItem>
    <TabItem label="Yarn">
        ```sh
        yarn astro add starlight
        ```
    </TabItem>

</Tabs>

Ele vai instalar as dependências necessárias e adicionar o Starlight no array `integrations` do seu arquivo de configuração do Astro.

### Configure a integração

A integração Starlight é configurada no seu arquivo `astro.config.mjs`.

Adicione um `title` para começar:

```js ins={8}
// astro.config.mjs
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

export default defineConfig({
	integrations: [
		starlight({
			title: 'Meu brilhante site de documentação',
		}),
	],
});
```

Encontre todas as opções disponíveis na [referência de configuração do Starlight](/pt-br/reference/configuration/).

### Configure coleções de conteúdo

Starlight é construído com base nas [coleções de conteúdo](https://docs.astro.build/pt-br/guides/content-collections/) do Astro, que são configuradas no arquivo `src/content/config.ts`.

Crie ou atualize o arquivo de configuração de conteúdo, adicionando uma coleção `docs` que usa o esquema `docsSchema` do Starlight:

```js ins={3,6}
// src/content/config.ts
import { defineCollection } from 'astro:content';
import { docsSchema } from '@astrojs/starlight/schema';

export const collections = {
	docs: defineCollection({ schema: docsSchema() }),
};
```

### Adicione conteúdo

Starlight agora está configurado e chegou a hora de adicionar algum conteúdo!

Crie um diretório `src/content/docs/` e comece por adicionar um arquivo `index.md`.
Essa será a página inicial do seu site:

```md
---
# src/content/docs/index.md
title: Minha documentação
description: Aprenda mais sobre meu projeto nesse site de documentação construído com Starlight.
---

Bem-vindo ao meu projeto!
```

Starlight usa roteamento baseado em arquivos, o que significa que todo arquivo Markdown, MDX ou Markdoc em `src/content/docs/` se tornará uma página no seu site. Metadados do frontmatter (os campos `title` e `description` no exemplo acima) podem mudar como cada página é mostrada.
Veja todas as opções disponíveis na [referência de frontmatter](/pt-br/reference/frontmatter/).

## Dicas para sites existentes

Se você tem um projeto Astro existente, você pode utilizar Starlight para rapidamente adicionar uma seção de documentação ao seu site.

### Use Starlight como um subcaminho

Para adicionar todas as páginas do Starlight em um subcaminho, coloque todo o conteúdo da sua documentação dentro de um subdiretório de `src/content/docs/`.

Por exemplo, se todas as páginas do Starlight devem começar com `/guias/`, adicione seu conteúdo no diretório `src/content/docs/guias/`:

import { FileTree } from '@astrojs/starlight/components';

<FileTree>

- src/
  - content/
    - docs/
      - **guias/**
        - guia.md
        - index.md
  - pages/
- astro.config.mjs

</FileTree>

No futuro, planejamos suportar esse caso de uso melhor para evitar a necessidade de um diretório aninhado adicional em `src/content/docs/`.

### Use Starlight com SSR

Você pode utilizar Starlight em conjunto com páginas renderizadas sob demanda em seu projeto seguindo o guia ["Adaptadores de renderização sob demanda"](https://docs.astro.build/pt-br/guides/server-side-rendering/) na documentação do Astro.

No momento, as páginas de documentação geradas pelo Starlight são sempre pré-renderizadas independentemente do modo de saída do seu projeto. Nós esperamos ser capazes de suportar renderização sob demanda para páginas Starlight em breve.
