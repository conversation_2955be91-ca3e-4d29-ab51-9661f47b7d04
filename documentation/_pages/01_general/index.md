---
title: General architecture
---

## Service types

Viclass employs microservices architecture. The services are classified into a few types of services

* Entity services - these provide API to access the data and hence abstract away the underlying data store. Each service type manages its own entity types and other service types can only read / write the entities through that service type. A service type might have multiple instances for load balancing or high availability. The data services are almost stateless. It prioritize statelessness and only keeps minimal state data unless absolutely necessary. Entity services also handle business logic related to the entities it manages, for example,
Lsession entity service provides methods to create / remove session entity, registration entities, etc... User entity service provides methods to create / remove / register / update / authenticate, etc... user entities.

* Business services -  these services provide the processing that calculate neccessary changes to the data. They invokes entity services to query necessary data and to persist the changes needed. Often, we need business services where it concerns multiple type of entities, running background processing or data agreegation, etc... Business services also provides API to other services.

* API services - these services often can also be business services since they also provides business logic calculation. However, they focus more on providing access the various functionalities of the system for frontend users, validating user inputs, and sending notifications to the users.

* Frontend services - runs on the browsers or other client, such as phone, tablet or special purpose devices. These services accept user input, display data and so on..

* Databases - Keeping states of the whole system. Databases are abstracted away, only Entity services can access them. There can be multiple databases but this is underlying details and should not concern higher level services.

* Message queues - for sending transferring change events of the system state. The change events are grouped in topics. Any service interested in the events can subscribe to the corresponding topic.

![High level data flow](https://drive.google.com/uc?export=view&id=1LkA4fvMcKqeBGiX8R0D-5mGpBy6R1oxt){:.page-image}
{:.text-center}

## Actual service diagram

The current services in the viclass system is like following

![High level data flow](https://drive.google.com/uc?export=view&id=1OmEnJGTmoCRhDx5VdSWKp-PWXUdCIG_h){:.page-image}
{:.text-center}
