<!doctype html>
<html>

<head>
  <!-- <script defer src="//unpkg.com/mathlive"></script> -->

  <!-- <script defer src="https://cdn.jsdelivr.net/npm/mathlive"></script> -->
  <title>untitled</title>
</head>

<body>
  <math-field id="input-field">x+1</math-field>

  <script type="module">
    // import { MathfieldElement } from "https://esm.run/mathlive";
    import { MathfieldElement } from "https://unpkg.com/mathlive?module";
    customElements
      .whenDefined("math-field")
      .then(() => {
        const mf2 = document.getElementById("input-field");
        mf2.value = "\\sqrt{x}";
        mf2.executeCommand("selectAll");
      });



  </script>
</body>

</html>