import { EditorUILookup } from '@viclass/editorui.loader';
import {
    Environment,
    MFEConfCreator,
    MFEConfRequest,
    MFEDescription,
    MFESpec,
    MFESpecCommonToolUISettings,
    MFESpecWordSettings,
    frag,
} from 'src/app.model';
import type { WordUISettings } from '@viclass/editorui.word';
import { ModuleLookup } from '@viclass/editor.core';

const embedEditorSettings = {
    iconClasses: {
        FreeDrawingEditor: 'vcon_document_freedrawing',
        GeometryEditor: 'vcon_document_geometry',
        MathEditor: 'vcon_document_mathtype',
        MathGraphEditor: 'vcon_document_magh',
    },
};

const myTheme = 'default';

export default async function (spec: MFESpec, env: Environment, request: MFEConfRequest): Promise<MFEDescription> {
    const uiLookup: EditorUILookup = {
        editorType: 'WordEditor',
        uiImpl: {
            type: 'module',
            remoteName: 'editorui.word',
            remoteEntry: `${env.scheme}://${env.domain}/modules/editorui.word/editorui.word.js`,
            exposedModule: './editorui.word',
        },
        style: {
            type: 'module',
            remoteName: 'editorui.word.style',
            remoteEntry: `${env.scheme}://${env.domain}/modules/themes/editorui.word.style.js`,
            exposedModule: './editorui.word.style',
        },
    };

    if (spec.ui && spec.ui !== true && spec.ui.settings) {
        const settings = spec.ui.settings as MFESpecWordSettings;

        if (settings.embedded) {
            const baseThemeLookupCreator = (await import(frag(`editor-tool-uis/${myTheme}/editor-ui-base-style`)))
                .default as MFEConfCreator;

            const baseThemeLookup = await baseThemeLookupCreator(
                {
                    item: 'editor-ui-base-style',
                    ui: true,
                },
                env,
                null
            );

            const lookupSettings: WordUISettings = {
                subEditorUILookups: [],
                iconClasses: {},
                subEditorUIBaseTheme: baseThemeLookup.ui as ModuleLookup,
            };

            for (const item of settings.embedded) {
                const editorUILookupCreator = (await import(frag(`editor-tool-uis/${myTheme}/${item}`)))
                    .default as MFEConfCreator;

                const editorUILookup = (await editorUILookupCreator({ item: item, ui: true }, env, null))
                    .ui as EditorUILookup;

                // generate the look up for the UI of the sub editors
                lookupSettings.subEditorUILookups.push(editorUILookup as EditorUILookup);
                lookupSettings.iconClasses[editorUILookup.editorType] =
                    embedEditorSettings.iconClasses[editorUILookup.editorType];
            }

            uiLookup.settings = lookupSettings;
        }
    }

    return {
        item: 'WordEditor',
        ui: uiLookup,
    };
}
