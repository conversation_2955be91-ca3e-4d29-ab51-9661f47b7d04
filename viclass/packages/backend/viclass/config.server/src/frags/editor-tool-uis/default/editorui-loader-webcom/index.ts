import { ModuleLookup } from '@viclass/editor.core';
import { Environment, MFEConfCreator, MFEConfRequest, MFEDescription, MFESpec } from 'src/app.model';

const create: MFEConfCreator = async (
    spec: MFESpec,
    env: Environment,
    request: MFEConfRequest
): Promise<MFEDescription> => {
    const lookup: ModuleLookup = {
        type: 'module',
        remoteName: 'editorui.loader.webcomp',
        remoteEntry: `${env.scheme}://${env.domain}/modules/mfe/editorui.loader.webcomp.js`,
        exposedModule: './editorui.loader.webcomp',
    };

    return {
        item: 'EditorUiLoaderWebComp',
        impl: lookup,
    };
};

export default create;
