/* You can add global styles to this file, and also import other style files */
@use '@viclass/portal.common/common_v3';
@use '@viclass/editorui.loader/style';

@use '@viclass/themes/common';
@use '@viclass/themes/button';
@use '@viclass/themes/form';

@import 'assets/icons/style.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

.side-bar-cover {
    @apply bg-BW7;
    right: 0;
    top: 0;
    margin-top: 80px;
    border-radius: 15px 0 0 15px;
    box-shadow: 0 5px 20px 0 rgba(0, 66, 75, 0.2);
}

.side-bar {
    @apply bg-BW7;
    right: 0;
    top: 0;
    bottom: 0;
    margin: 80px 0 60px 0;
    border-radius: 15px 0 0 15px;
    box-shadow: 0 5px 20px 0 rgba(0, 66, 75, 0.2);

    .sidebar-item {
        @apply text-BW2;
        height: 45px;
        position: relative;
        width: 100%;
        align-items: center;

        .sidebar-item-overlay {
            @apply bg-P3 text-BW2;
            position: absolute;
            overflow: hidden;
            width: 100%;
            height: 100%;
            transition: 0s ease;
            visibility: hidden;
            align-items: center;

            button {
                @apply text-BW2;
                background-color: transparent;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                padding: 5px;
                margin: 13px 0 13px 0;
                border: none;
                align-content: center;
                align-items: center;
                text-align: center;
                font-size: 20px;
                line-height: 20px;

                &:hover {
                    @apply bg-BW7;
                    @apply text-BW1;
                }
            }
        }

        &:hover {
            .sidebar-item-overlay {
                visibility: visible;
            }
        }

        &.sidebar-item-active {
            @apply bg-P2;
            @apply text-BW1;

            .sidebar-item-overlay {
                @apply bg-P2;
            }
        }

        .mat-icon-button {
            @apply text-BW4;
            height: 100%;
            text-align: center;
            display: none;
            margin-left: auto;

            &:hover {
                @apply text-BW7;
            }
        }

        &:hover > .mat-icon-button {
            display: block;
        }

        .member-item-avatar {
            align-items: center;
        }

        &.active {
            @apply bg-P2;
        }
    }

    .member-item-avatar {
        position: relative;
        width: 56px;
        height: 56px;

        &.member-raise-hand-avatar {
            background-image: url(assets/img/raise-hand-avatar.gif);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position-x: -5px;
            background-position-y: 3px;
        }

        .member-speaking-request {
            position: absolute;
            right: 5px;
            top: 10px;
            background-image: url(assets/img/speaking-request.gif);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            width: 16px;
            height: 16px;
            border-radius: 10px;
        }

        .member-avatar-tab {
            position: relative;
            width: 30px;
            height: 30px;
            align-self: center;

            .member-avatar {
                @apply text-BW4;
                @apply border border-BW4;
                width: 100%;
                height: 100%;
                border-radius: 50%;
                background-size: cover;
            }

            .member-waiting-tag {
                width: 16px;
                height: 16px;
                position: absolute;
                font-size: 16px;
                border-radius: 50%;
                margin: -2.5px -7px 0 0;
            }

            .member-user-status {
                width: 11px;
                height: 11px;
                position: absolute;
                font-size: 11px;
                line-height: 11px;
                border-radius: 50%;
                margin: 18.5px -5px 0 0;
            }
        }
    }

    .sidebar-desc {
        @apply text-BW2;
        font-size: 12px;
        line-height: 12px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    .member-desc-note {
        @apply text-BW2;
        font-size: 12px;
        line-height: 12px;
        font-style: italic;
        display: inline-block;
    }

    .badge-blank {
        &:after {
            @apply bg-SC5;
            content: '';
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            position: absolute;
        }
    }
}

.page-base-dot {
    background-color: #fff;
}

.viewport {
    position: relative;
    overflow: hidden;
    z-index: 0;

    editor-ui-group {
        height: fit-content;
        max-height: calc(100vh - 50px);
        width: fit-content;
        max-width: 100%;
        overflow-y: auto;
        overflow-x: visible;
        scrollbar-width: none;
        pointer-events: none;

        &::-webkit-scrollbar {
            display: none;
        }
    }
}

.hover-switch-icon {
    .show-default {
        display: inline-block;
    }

    .show-hover {
        display: none;
    }

    &:hover {
        .show-default {
            display: none;
        }

        .show-hover {
            display: inline-block;
        }
    }
}

.raise-hand-control {
    .raise-hand-none {
        i {
            display: inline-block;
        }

        img {
            display: none;
        }

        &:hover {
            background-image: var(--P2);

            i {
                display: none;
            }

            img {
                display: inline-block;
            }
        }
    }

    .presenting,
    .raise-hand {
        i {
            display: none;
        }

        img {
            display: inline-block;
        }
    }

    .presenting,
    .raise-hand {
        &:hover {
            i {
                display: inline-block;
            }

            img {
                display: none;
            }
        }
    }

    button {
        @apply bg-P2;
    }
}

.quick-question-box {
    @apply bg-BW7;
    gap: 10px;
    width: 270px;
    height: 210px;
    box-sizing: border-box;
    border: solid 5px rgb(var(--P1));
    border-radius: 20px;
    transition: box-shadow 200ms cubic-bezier(0, 0, 0.2, 1);
    z-index: 5;
    position: absolute;

    &:active {
        box-shadow:
            0 5px 5px -3px rgba(0, 0, 0, 0.2),
            0 8px 10px 1px rgba(0, 0, 0, 0.14),
            0 3px 14px 2px rgba(0, 0, 0, 0.12);
    }

    .quick-question-box-handle {
        width: 100%;
        font-weight: bold;
        cursor: move;
        text-align: center;
        padding: 5px;
        border-radius: 15px 15px 0 0;
    }

    .question-content {
        @apply text-BW1;
        padding: 0 0 0 0;
        border: none;
        width: 100%;
        height: 143px;
        background: transparent;
        resize: none;
        cursor: text;
        overflow: auto;

        &:focus-visible {
            outline: none;
        }

        &::-webkit-scrollbar {
            width: 14px;
            @apply bg-BW5;
        }

        &::-webkit-scrollbar-thumb {
            @apply bg-BW4;
            border: 5px solid rgb(var(--BW7));
            border-radius: 10px;
        }

        &::-webkit-scrollbar-track {
            @apply bg-BW7;
            @apply border-none;
        }
    }
}

.vi-popup {
    @apply bg-TP2;
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 1000;

    .vi-popup-container {
        @apply bg-BW7;
        display: flex;
        position: fixed;
        top: calc(50% - 100px);
        left: calc(50% - 175px);
        margin-left: auto;
        margin-right: auto;
        width: 350px;
        height: 250px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        -webkit-flex-shrink: 0;
        border-radius: 30px;
        border: solid 1px rgb(var(--BW3));
        padding: 0 25px;

        .vi-popup-message {
            @apply text-BW2;
            font-size: 14px;
            font-family: Montserrat;
            font-style: normal;
            font-weight: 500;
            line-height: 20px;
        }

        .vi-popup-note {
            @apply bg-BW5;
            @apply text-BW3;
            font-size: 14px;
            font-family: Montserrat;
            font-style: normal;
            font-weight: 500;
            line-height: 20px;
            border-radius: 0px 0px 30px 30px;
            padding: 5px 25px;
            width: 348px;
            justify-content: center;
            align-items: center;
            margin-top: auto;
            display: grid;
            justify-items: center;
        }
    }

    .vi-input {
        &::-webkit-scrollbar {
            width: 14px;
            @apply bg-BW5;
        }

        &::-webkit-scrollbar-thumb {
            @apply bg-BW4;
            border: 5px solid rgb(var(--BW5));
            border-radius: 10px;
        }

        &::-webkit-scrollbar-track {
            @apply bg-BW5;
            @apply border-none;
        }
    }
}

.shadow-right {
    position: relative;

    &:after {
        content: '';
        width: 20px;
        height: 100%;
        position: absolute;
        left: 20px;
        box-shadow: inset 20px 0px 20px -20px rgba(102, 213, 255, 0.4);
        z-index: 1;
        pointer-events: none;
    }
}

.shadow-left {
    position: relative;

    &:before {
        content: '';
        width: 20px;
        height: 100%;
        position: absolute;
        right: 20px;
        box-shadow: inset -20px 0px 20px -20px rgba(102, 213, 255, 0.4);
        z-index: 1;
        pointer-events: none;
    }
}

.full-screen {
    .hide-on-full-screen {
        display: none !important;
    }
}

.viclass-sticky-layer {
    pointer-events: none;
    rect {
        pointer-events: none;
    }
}

.more-icon {
    display: flex;
    gap: 2px;
    align-items: center;
    justify-content: center;
    padding: 5px;
    background-color: #ffffffcc;
    border-radius: 10px;

    span {
        width: 4px;
        height: 4px;
        background-color: #333;
        border-radius: 50%;
    }
}

.marker-avatar-group {
    display: block;
    transition: transform 0.2s ease;

    &.presenting,
    &.idle {
        display: none;
    }

    .marker-icon {
        opacity: 0.8;
    }
}

.marker-touch-action-none {
    [contenteditable='true'] {
        touch-action: none !important;
    }
}
