import { Injectable } from '@angular/core';
import {
    LSessionRegistrationModel,
    LSessionService,
    NotificationService,
    ProcessingRequestManager,
    RaiseHandStatus,
} from '@viclass/portal.common';
import { firstValueFrom, from, ReplaySubject } from 'rxjs';
import { AppStateService } from '../app.state.service';
import { CcsGateway } from '../gateways/ccs.gateway';
import { MemberActionEvent, MemberItemEventType, PopupConfirmType } from '../model';
import { ClassroomConferenceService } from './conference/classroom.conference.service';
import { MemberStateService } from './member.state.service';
import { OnlineStateService } from './online.state.service';
import { classroomErrorHandler } from './error-handler';
import { ErrorHandlerDecorator } from '@viclass/editor.core';

@Injectable()
export class MemberActionListener {
    readonly memberActionEvent$: ReplaySubject<MemberActionEvent> = new ReplaySubject();

    constructor(
        private prm: ProcessingRequestManager,
        private as: AppStateService,
        private ccsGateway: CcsGateway,
        private lsessionS: LSessionService,
        private memberStateS: MemberStateService,
        private onlineStateS: OnlineStateService,
        private confS: ClassroomConferenceService,
        private notificationService: NotificationService
    ) {}

    start() {
        this.memberActionEvent$.subscribe(e => this.handleActionEvent(e));
    }

    actionInProgressName(regId: string, action: MemberItemEventType | '' = ''): string {
        return `member-${regId}-action-${action}`;
    }

    /**
     * Monitors an asynchronous task related to a member.
     * @template T The type of the result of the task.
     * @param taskCallback A function that executes the task and returns a Promise.
     * @param regId The ID of the member (defaults to 'global' if the action is not tied to a specific member).
     * @param action The name of the action (optional).
     * @returns A Promise that resolves with the result of the task.
     */
    private async monitorMemberAction<T>(
        taskCallback: () => Promise<T>,
        regId: string = 'global',
        action: MemberItemEventType | '' = ''
    ): Promise<T> {
        const monitorKey = this.actionInProgressName(regId, action);
        const task$ = from(taskCallback()); // Assuming 'from' is an RxJS operator
        // Assuming 'this.prm.monitor' is a method for monitoring Observables
        return await firstValueFrom(this.prm.monitor(monitorKey, task$, { parent: `member-${regId}` }));
    }

    private updateRaiseHandStatus(member: LSessionRegistrationModel, status: RaiseHandStatus) {
        member.userState.raiseHandStatus = status;
        this.memberStateS.update(member);
    }

    @ErrorHandlerDecorator([classroomErrorHandler])
    private async handleActionEvent(e: MemberActionEvent) {
        await this.monitorMemberAction(
            async () => {
                const member = this.memberStateS.getMember(e.regId);
                const isOwner = await firstValueFrom(this.onlineStateS.isOwner$);
                const coord = await firstValueFrom(this.onlineStateS.coordinator$);

                if (!isOwner) return;

                switch (e.action) {
                    case 'approve-present': {
                        let res: string;
                        const coord = await firstValueFrom(this.onlineStateS.coordinator$);
                        const prePresentingCoord = coord.getCoordState(coord.roomInfo.presentingCoordState);
                        if (
                            !prePresentingCoord.default &&
                            !coord.roomInfo.pinnedCoordStates.includes(prePresentingCoord.id)
                        ) {
                            const obs = new ReplaySubject<PopupConfirmType>();
                            this.onlineStateS.confirmPresenterSwitchMessagePopup$.next({ obs });
                            res = await firstValueFrom(obs);
                            this.onlineStateS.confirmPresenterSwitchMessagePopup$.next(undefined);

                            if (res == 'yes') {
                                await this.onlineStateS.shareScreenService.removeAllShareScreenDocs(
                                    prePresentingCoord.id
                                );
                            }
                        }

                        if (res == 'yes' || !res) {
                            await firstValueFrom(
                                this.ccsGateway.acceptRaiseHand({
                                    lsId: this.as.lsId,
                                    callingPeerId: coord.peerId,
                                    targetUserId: member.profile.id,
                                })
                            );
                            const currentPresent = this.memberStateS.filterMember(
                                m => m.userState.raiseHandStatus == 'PRESENTING'
                            )[0];
                            if (currentPresent) {
                                const currMember = this.memberStateS.getMember(currentPresent.id);
                                this.updateRaiseHandStatus(currMember, 'NONE');
                            }
                            this.updateRaiseHandStatus(member, 'PRESENTING');
                        }
                        break;
                    }
                    case 'reject-present': {
                        await firstValueFrom(
                            this.ccsGateway.rejectRaiseHand({
                                lsId: this.as.lsId,
                                callingPeerId: coord.peerId,
                                targetUserId: member.profile.id,
                            })
                        );
                        this.updateRaiseHandStatus(member, 'NONE');
                        break;
                    }
                    case 'accept-share-screen': {
                        await firstValueFrom(
                            this.ccsGateway.acceptShareScreen({
                                lsId: this.as.lsId,
                                callingPeerId: coord.peerId,
                                targetUserId: member.profile.id,
                            })
                        );
                        break;
                    }

                    case 'reject-share-screen': {
                        await firstValueFrom(
                            this.ccsGateway.rejectShareScreen({
                                lsId: this.as.lsId,
                                callingPeerId: coord.peerId,
                                targetUserId: member.profile.id,
                            })
                        );

                        break;
                    }

                    case 'approve-register': {
                        const currentMembers = await firstValueFrom(this.memberStateS.members$);
                        const registeredMembers = currentMembers.filter(m => m.regStatus == 'REGISTERED');
                        const maxRegs = (await firstValueFrom(this.lsessionS.settings$.get(this.as.lsId)))
                            .maxRegistration;

                        if (registeredMembers.length >= maxRegs) {
                            this.notificationService.showNotification({
                                message: 'Lớp học đã đầy. Không thể thêm thành viên mới.',
                                status: 'error',
                            });
                            break;
                        }

                        await firstValueFrom(this.lsessionS.approveRegistration(e.regId));
                        member.regStatus = 'REGISTERED';
                        this.memberStateS.update(member);

                        // user already know about the new registers -> reset the badge
                        this.memberStateS.newMemberWaitingConfirm$.next(0);
                        break;
                    }
                    case 'kick-out':
                    case 'reject-register': {
                        const obs = new ReplaySubject<PopupConfirmType>();
                        this.onlineStateS.confirmKickOutMemberPopup$.next({
                            obs: obs,
                            member: member,
                        });
                        const res = await firstValueFrom(obs);
                        this.onlineStateS.confirmKickOutMemberPopup$.next(undefined);
                        if (res == 'yes') {
                            if (member.userState.raiseHandStatus == 'PRESENTING') {
                                await firstValueFrom(
                                    this.ccsGateway.stopPresentation({
                                        lsId: this.as.lsId,
                                        callingPeerId: coord.peerId,
                                        targetUserId: member.profile.id,
                                    })
                                );
                                member.userState.raiseHandStatus = 'NONE';
                                this.memberStateS.update(member);
                            }
                            await firstValueFrom(this.lsessionS.rejectRegistration(e.regId));
                            member.regStatus = 'REJECTED';
                            this.memberStateS.update(member);
                            // Check if the received action is a "kick-out" event.
                            if (e.action == 'kick-out') {
                                // Call the function to kick out the user from the conference.
                                // 'member.profile.id' contains the unique identifier for the user that needs to be removed.
                                this.confS.kickOutUser(member.profile.id);
                            }
                        }

                        if (e.action == 'reject-register') {
                            // user already know about the new registers -> reset the badge
                            this.memberStateS.newMemberWaitingConfirm$.next(0);
                        }
                        break;
                    }
                    case 'request-presentation': {
                        let res: string;
                        const coord = await firstValueFrom(this.onlineStateS.coordinator$);
                        const prePresentingCoord = coord.getCoordState(coord.roomInfo.presentingCoordState);
                        if (
                            !prePresentingCoord.default &&
                            !coord.roomInfo.pinnedCoordStates.includes(prePresentingCoord.id)
                        ) {
                            const obs = new ReplaySubject<PopupConfirmType>();
                            this.onlineStateS.confirmPresenterSwitchMessagePopup$.next({ obs });
                            res = await firstValueFrom(obs);
                            this.onlineStateS.confirmPresenterSwitchMessagePopup$.next(undefined);
                        }

                        if ((res && res == 'yes') || !res) {
                            await firstValueFrom(
                                this.ccsGateway.requestPresentation({
                                    lsId: this.as.lsId,
                                    callingPeerId: coord.peerId,
                                    targetUserId: member.profile.id,
                                })
                            );
                            member.userState.raiseHandStatus = 'NONE';
                            this.memberStateS.update(member);
                        }
                        break;
                    }
                    case 'cancel-request-presentation': {
                        await firstValueFrom(
                            this.ccsGateway.cancelRequestPresentation({
                                lsId: this.as.lsId,
                                callingPeerId: coord.peerId,
                                targetUserId: member.profile.id,
                                activityId: e.activityId,
                            })
                        );
                        member.userState.raiseHandStatus = 'NONE';
                        this.memberStateS.update(member);
                        break;
                    }
                    case 'stop-present': {
                        let res: string;
                        const coord = await firstValueFrom(this.onlineStateS.coordinator$);
                        const prePresentingCoord = coord.getCoordState(coord.roomInfo.presentingCoordState);
                        if (
                            !prePresentingCoord.default &&
                            !coord.roomInfo.pinnedCoordStates.includes(prePresentingCoord.id)
                        ) {
                            const obs = new ReplaySubject<PopupConfirmType>();
                            this.onlineStateS.confirmPresenterSwitchMessagePopup$.next({ obs });
                            res = await firstValueFrom(obs);
                            this.onlineStateS.confirmPresenterSwitchMessagePopup$.next(undefined);

                            if (res == 'yes') {
                                await this.onlineStateS.shareScreenService.removeAllShareScreenDocs(
                                    prePresentingCoord.id
                                );
                            }
                        }

                        if ((res && res == 'yes') || !res) {
                            await firstValueFrom(
                                this.ccsGateway.stopPresentation({
                                    lsId: this.as.lsId,
                                    callingPeerId: coord.peerId,
                                    targetUserId: member.profile.id,
                                })
                            );
                            member.userState.raiseHandStatus = 'NONE';
                            this.memberStateS.update(member);
                        }

                        break;
                    }
                    case 'reject-request-pin-board': {
                        const coord = await firstValueFrom(this.onlineStateS.coordinator$);
                        await coord.rejectRequestPinBoard(member.profile.id, e.coordStateId);
                        member.userState.requestPinTabState.filter(s => s.tabId == e.coordStateId)[0].status =
                            'REJECTED';
                        this.memberStateS.update(member);
                        break;
                    }
                    case 'approve-request-pin-board': {
                        const coord = await firstValueFrom(this.onlineStateS.coordinator$);
                        await coord.approveRequestPinBoard(member.profile.id, e.coordStateId);
                        member.userState.requestPinTabState.filter(s => s.tabId == e.coordStateId)[0].status =
                            'APPROVED';
                        this.memberStateS.update(member);
                        break;
                    }

                    default:
                        break;
                }
            },
            e.regId,
            e.action
        );
    }
}
