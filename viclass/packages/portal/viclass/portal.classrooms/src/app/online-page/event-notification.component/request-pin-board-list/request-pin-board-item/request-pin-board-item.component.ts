import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { As<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/common';
import {
    LSessionRegistrationModel,
    ProcessingRequestManager,
    SpinnerLabelComponent,
    TooltipComponent,
} from '@viclass/portal.common';
import { map, Observable } from 'rxjs';
import { MemberActionEvent, MemberAvatarViewModel, MemberItemEventType } from '../../../../model';
import { OnlineStateService } from '../../../online.state.service';
import { EventNotiService } from '../../event.service';
import { MemberActionListener } from '../../../member.action.listener';

@Component({
    selector: 'app-request-pin-board-item',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [<PERSON><PERSON><PERSON><PERSON>, <PERSON>I<PERSON>, <PERSON><PERSON><PERSON>, TooltipComponent, Async<PERSON>ipe, SpinnerLabelComponent],
    templateUrl: './request-pin-board-item.component.html',
    styleUrls: ['./request-pin-board-item.component.scss'],
})
export class RequestPinBoardItemComponent implements OnInit {
    private _accessButtons = false; // Track hover state

    @Input() userId: string;
    @Input() regId: string;
    @Input() coordStateId: string;
    @Input() tabName: string;

    constructor(
        public onlStateS: OnlineStateService,
        private cdr: ChangeDetectorRef,
        private eventNotiS: EventNotiService,
        private prm: ProcessingRequestManager,
        private memberActionListener: MemberActionListener
    ) {}

    ngOnInit(): void {}

    protected get actionInProgress$(): Observable<boolean> {
        return this.prm.getInprogressObs(`member-${this.regId}`);
    }

    protected get avatarUrl$() {
        return this.avatarModel$.pipe(map(m => m.avatarUrl));
    }

    protected get avatarModel$(): Observable<MemberAvatarViewModel> {
        return this.eventNotiS.avatarModel$(this.userId);
    }

    protected get member$(): Observable<LSessionRegistrationModel> {
        return this.eventNotiS.member$(this.userId);
    }

    protected onMemberAction(type: MemberItemEventType) {
        const event: MemberActionEvent = {
            action: type,
            regId: this.regId,
            coordStateId: this.coordStateId,
        };
        this.memberActionListener.memberActionEvent$.next(event);
    }

    protected onMouseEnter() {
        this._accessButtons = true;
        this.cdr.markForCheck(); // Trigger change detection on hover
    }

    protected onMouseLeave() {
        this._accessButtons = false;
        this.cdr.markForCheck(); // Trigger change detection on hover out
    }

    protected get accessButtons$(): Observable<boolean> {
        return this.onlStateS.isOwner$.pipe(
            map(isOwner => {
                return isOwner && this._accessButtons;
            })
        );
    }
}
