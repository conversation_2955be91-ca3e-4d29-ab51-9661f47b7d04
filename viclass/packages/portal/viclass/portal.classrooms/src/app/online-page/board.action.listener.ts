import { Injectable } from '@angular/core';
import { ErrorHandlerDecorator, ViewportId } from '@viclass/editor.core';
import { ProcessingRequestManager, UserService } from '@viclass/portal.common';
import { filter, first, firstValueFrom, from, ReplaySubject, take } from 'rxjs';
import { BoardTabControlEvent, BoardTabControlEventType, PopupConfirmType } from '../model';
import { CoordStatesService } from './coord.state.service';
import { OnlineStateService } from './online.state.service';
import { ViewportService } from './viewport.service';
import { ClassroomCoordinator } from '@viclass/editor.coordinator/classroom';
import { MemberStateService } from './member.state.service';
import { classroomErrorHandler } from './error-handler';

@Injectable()
export class BoardActionListener {
    readonly boardActionEvent$: ReplaySubject<BoardTabControlEvent>;

    constructor(
        private readonly prm: ProcessingRequestManager,
        private readonly onlineStateS: OnlineStateService,
        private readonly coordStatesS: CoordStatesService,
        private readonly viewportS: ViewportService,
        private readonly memberService: MemberStateService,
        private readonly uS: UserService
    ) {
        this.boardActionEvent$ = new ReplaySubject();
    }

    start() {
        return this.boardActionEvent$.subscribe(e => this.handleBoardEvent(e));
    }

    actionInProgressName(boardId: string, action: BoardTabControlEventType | '' = ''): string {
        return `board-${boardId}-action-${action}`;
    }

    /**
     * Monitors an asynchronous task related to a board.
     * @template T The type of the result of the task.
     * @param taskCallback A function that executes the task and returns a Promise.
     * @param boardId The ID of the board (defaults to 'global' if the action is not tied to a specific board).
     * @param action The name of the action (optional).
     * @returns A Promise that resolves with the result of the task.
     */
    private async monitorBoardAction<T>(
        taskCallback: () => Promise<T>,
        boardId: string = 'global',
        action: BoardTabControlEventType | '' = ''
    ): Promise<T> {
        const monitorKey = this.actionInProgressName(boardId, action);
        const task$ = from(taskCallback());
        return await firstValueFrom(this.prm.monitor(monitorKey, task$, { parent: `board-${boardId}` }));
    }

    async presentBoard(coord: ClassroomCoordinator, viewportId: ViewportId) {
        await coord.presentCoordState(viewportId);
        const vpMode = await this.onlineStateS.calculateViewportMode(viewportId);
        await coord.switchViewportMode(viewportId, vpMode);
        await coord.switchViewport(viewportId);
        this.coordStatesS.setPresenting(viewportId);
    }

    /**
     * Handle event form board, when user do some action with the board
     * @param event
     */
    @ErrorHandlerDecorator([classroomErrorHandler])
    private async handleBoardEvent(event: BoardTabControlEvent) {
        await this.monitorBoardAction(
            async () => {
                const coord = await firstValueFrom(this.onlineStateS.coordinator$);
                switch (event.type) {
                    case 'new-board': {
                        // Filter boards owned by the current user
                        const curStatesData = await firstValueFrom(this.coordStatesS.curStates$.pipe(first()));
                        const curStates = curStatesData.map(s => s.value);
                        const userBoardList = curStates.filter(b => b.owner === coord.userId); // Giả sử coord từ scope ngoài vẫn hợp lệ
                        // Find boards that follow the default naming pattern "Bảng [number]"
                        let boardsWithDefaultNamePattern = userBoardList.filter(b => /^Bảng [0-9]+?$/.test(b.title));

                        let title = 'Bảng 1';
                        if (boardsWithDefaultNamePattern.length) {
                            // Sort boards by their numeric suffix to find the highest number
                            boardsWithDefaultNamePattern = boardsWithDefaultNamePattern.sort((a, b) => {
                                const aNum = parseInt(a.title.split(' ')[1]);
                                const bNum = parseInt(b.title.split(' ')[1]);
                                return aNum - bNum;
                            });
                            // Get the board with the highest number
                            const lastBoard = boardsWithDefaultNamePattern[boardsWithDefaultNamePattern.length - 1];
                            const lastNum = parseInt(lastBoard.title.split(' ')[1]);
                            title = `Bảng ${lastNum + 1}`;
                        }

                        // Thực hiện các tác vụ tạo board tuần tự
                        const cs = await coord.createCoordinatorState(title);
                        this.coordStatesS.add(cs);

                        // wait for the new board to be ready
                        await firstValueFrom(
                            this.viewportS.readyViewportCount$.pipe(
                                filter(() => this.viewportS.viewports.some(v => v.state.id === cs.id)),
                                take(1)
                            )
                        );
                        // switch to the new board
                        const vpMode = await this.onlineStateS.calculateViewportMode(cs.id);
                        await coord.switchViewportMode(cs.id, vpMode);
                        // disable old view port, active and select new view port
                        await coord.switchViewport(cs.id);
                        break;
                    }
                    case 'select-board': {
                        await this.switchToViewportId(event.id);
                        break;
                    }
                    case 'remove-board': {
                        const coordState = coord.getCoordState(event.id);
                        const obs = new ReplaySubject<PopupConfirmType>();
                        this.onlineStateS.confirmDeletePagePopup$.next({ obs, pageName: coordState.title });
                        const res = await firstValueFrom(obs);
                        this.onlineStateS.confirmDeletePagePopup$.next(undefined);

                        if (res == 'yes') {
                            // switch to presenting tab if the removed board is selected
                            if (this.coordStatesS.isSelected(event.id)) {
                                await this.switchToViewportId(coord.roomInfo.presentingCoordState);
                            }

                            await coord.deleteCoordinatorState(event.id);
                            this.coordStatesS.remove(event.id);
                        }
                        break;
                    }
                    case 'duplicate-board': {
                        const cs = await coord.duplicateCoordinatorState(event.id);
                        this.coordStatesS.add(cs);
                        await firstValueFrom(
                            this.viewportS.readyViewportCount$.pipe(
                                filter(() => this.viewportS.viewports.some(v => v.state.id === cs.id)),
                                take(1)
                            )
                        );
                        const vpMode = await this.onlineStateS.calculateViewportMode(cs.id);
                        await coord.switchViewportMode(cs.id, vpMode);
                        // disable old view port, active and select new view port
                        await coord.switchViewport(cs.id);
                        break;
                    }
                    case 'present-board': {
                        const prePresentingCoord = coord.getCoordState(coord.roomInfo.presentingCoordState);
                        if (
                            !prePresentingCoord.default &&
                            !coord.roomInfo.pinnedCoordStates.includes(prePresentingCoord.id)
                        ) {
                            const obs = new ReplaySubject<PopupConfirmType>();
                            this.onlineStateS.confirmViewportSwitchMessagePopup$.next({ obs });
                            const res = await firstValueFrom(obs);
                            this.onlineStateS.confirmViewportSwitchMessagePopup$.next(undefined);

                            if (res == 'yes') {
                                if (this.coordStatesS.isSelected(event.id)) {
                                    await this.switchToViewportId(coord.roomInfo.presentingCoordState);
                                }

                                await this.onlineStateS.shareScreenService.removeAllShareScreenDocs(
                                    prePresentingCoord.id
                                );
                                await this.presentBoard(coord, event.id);
                            }
                        } else {
                            await this.presentBoard(coord, event.id);
                        }
                        break;
                    }
                    case 'request-pin-board': {
                        await coord.requestPinBoard(event.id);
                        break;
                    }
                    case 'cancel-request-pin-board': {
                        await coord.cancelRequestPinBoard(event.id);
                        break;
                    }
                    case 'pin-board': {
                        await coord.pinCoordinatorState(event.id);
                        break;
                    }
                    case 'unpin-board': {
                        const coordState = coord.getCoordState(event.id);
                        const isNotPresenting = !coordState.presenting;
                        const isNotPresenter =
                            this.memberService.getMemberByUserId(this.uS.curUser$.value.id).userState
                                .raiseHandStatus !== 'PRESENTING';

                        if (isNotPresenting || isNotPresenter) {
                            const obs = new ReplaySubject<PopupConfirmType>();
                            this.onlineStateS.confirmPresenterSwitchMessagePopup$.next({ obs });
                            const res = await firstValueFrom(obs);
                            this.onlineStateS.confirmPresenterSwitchMessagePopup$.next(undefined);

                            if (res !== 'yes') return;
                        }

                        if (isNotPresenting) {
                            await this.onlineStateS.shareScreenService.removeAllShareScreenDocs(event.id);
                            await coord.unpinCoordinatorState(event.id);
                        } else {
                            await coord.unpinCoordinatorState(event.id);
                        }
                        break;
                    }
                    case 'rename-board': {
                        const coordState = this.coordStatesS.get(event.id);
                        await coord.renameCoordinatorState(event.id, event.data);
                        coordState.title = event.data;
                        this.coordStatesS.update(coordState);
                        break;
                    }
                    default:
                        break;
                }
            },
            event.id,
            event.type
        );
    }

    /**
     * Switch to the target viewport and change the viewport mode
     *
     * @param vpId the target viewport id
     */
    private async switchToViewportId(vpId: ViewportId): Promise<void> {
        const coord = await firstValueFrom(this.onlineStateS.coordinator$);
        const vpMode = await this.onlineStateS.calculateViewportMode(vpId);
        await coord.switchViewportMode(vpId, vpMode);
        await coord.switchViewport(vpId);
    }
}
