import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CommonModule, NgOptimizedImage } from '@angular/common';
import { LSessionRegistrationModel, LSessionService } from '@viclass/portal.common';
import { map, Observable, ReplaySubject } from 'rxjs';
import { AppStateService } from '../../../app.state.service';
import { PopupConfirmType } from '../../../model';

@Component({
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [CommonModule, NgOptimizedImage],
    selector: '[offline-member-popup]',
    templateUrl: './offline.member.popup.component.html',
})
export class OfflineMemberPopupComponent {
    @Input('member')
    member: LSessionRegistrationModel;

    @Input('offline-member-popup')
    confirm$: ReplaySubject<PopupConfirmType>;

    /**
     * popup appear in 60s
     * @protected
     */
    // protected readonly counter$ = interval(1000).pipe(
    //     take(61),
    //     map(s => 60 - s)
    // );

    constructor(
        private as: AppStateService,
        private lsessionS: LSessionService
    ) {
        // lastValueFrom(this.counter$).then(_ => this.action('cancel'));
    }

    protected get username(): string {
        return this.member.profile.username;
    }

    protected action(type: PopupConfirmType) {
        this.confirm$.next(type);
        this.confirm$.complete();
    }

    protected isOwner$(): Observable<boolean> {
        const ls = this.lsessionS.details$.get(this.as.lsId);
        return ls.pipe(
            map(it => {
                return it.creatorId == this.member.profile.id;
            })
        );
    }
}
