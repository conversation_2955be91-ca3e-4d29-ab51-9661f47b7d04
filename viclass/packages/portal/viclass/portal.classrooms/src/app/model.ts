import { RTCReport } from '@viclass/editor.coordinator/classroom';
import { DocumentId, EditorType, SpecialKeyboard, ViewportId } from '@viclass/editor.core';
import {
    LSessionDetails,
    LSessionSettings,
    LSRegStatus,
    RaiseHandStatus,
    UserAvailableStatus,
    UserProfile,
} from '@viclass/portal.common';
import { DocumentInfoDetail } from './gateways/ccs.model';

/**
 * The kinds of events generated by the user when perform an action for himself
 */
export type UserControlEventType =
    | 'leave'
    | 'start-record'
    | 'stop-record'
    | 'share'
    | 'raise-hand'
    | 'cancel-raise-hand'
    | 'stop-present'
    | 'accept-present-request'
    | 'reject-present-request'
    | 'accept-question'
    | 'reject-question'
    | 'new-question'
    | 'stop-question'
    | 'req-share-screen'
    | 'cancel-share-screen';

/**
 * The kinds of events generated by the user when perform an action for other member or for the teacher
 */
export type MemberItemEventType =
    | 'kick-out'
    | 'approve-register'
    | 'reject-register'
    | 'approve-present'
    | 'reject-present'
    | 'stop-present'
    | 'request-presentation'
    | 'cancel-request-presentation'
    | 'reject-share-screen'
    | 'accept-share-screen'
    | 'approve-request-pin-board'
    | 'reject-request-pin-board';

export type PopupConfirmType = 'yes' | 'no' | 'cancel';

export type BoardTabControlEventType =
    | 'new-board'
    | 'remove-board'
    | 'select-board'
    | 'present-board'
    | 'rename-board'
    | 'pin-board'
    | 'unpin-board'
    | 'duplicate-board'
    | 'request-pin-board'
    | 'cancel-request-pin-board';

export interface BoardTabControlEvent {
    type: BoardTabControlEventType;
    id: ViewportId;
    data?: any;
}

export type DocCtrlEventType =
    | 'update-doc-info'
    | 'duplicate-doc'
    | 'remove-doc'
    | 'select-doc'
    | 'share-doc'
    | 'copy-doc'
    | 'fit-to-view';

export interface DocCtrlEvent {
    type: DocCtrlEventType;
    keys?: SpecialKeyboard[];
    docGlobalId: DocumentId;
    data?: any;
}

export interface MemberAvatarViewModel {
    username: string;
    avatarUrl: string;
    activityStatus: RaiseHandStatus;
    availableStatus?: UserAvailableStatus;
    lsRegStatus: LSRegStatus;
    showWaitResponse: boolean; // whether to show an icon signifying this member being waited for responding a request
}

export interface MemberActionEvent {
    regId: string;
    activityId?: string;
    coordStateId?: string;
    action: MemberItemEventType;
}

export interface UserStatusReport {
    roomId: string;
    peerId: string;
    availableStatus: UserAvailableStatus;
    rtcConn: RTCReport;
}

export interface LSessionBasicInfo {
    details: LSessionDetails;
    settings: LSessionSettings;
    owner: UserProfile;
}

export interface DocumentInfo {
    docGlobalId: DocumentId;
    editorType: EditorType;
    details: DocumentInfoDetail;
    isSelected?: boolean;
}

export const EDITOR_ICONS: Partial<Record<EditorType, string>> = {
    FreeDrawingEditor: 'vcon_document_freedrawing',
    GeometryEditor: 'vcon_document_geometry',
    MathEditor: 'vcon_document_mathtype',
    MathGraphEditor: 'vcon_document_magh',
    PdfEditor: 'vcon_document_pdf',
    WordEditor: 'vcon_document_word',
};

export const EDITOR_NAMES: Partial<Record<EditorType, string>> = {
    GeometryEditor: 'Hình học',
    FreeDrawingEditor: 'Vẽ tự do',
    WordEditor: 'Văn bản',
    MathEditor: 'Công thức toán',
    MathGraphEditor: 'Đồ thị hàm số',
};
