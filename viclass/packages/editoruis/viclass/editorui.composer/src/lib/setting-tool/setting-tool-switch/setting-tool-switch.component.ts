import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { SettingFieldChangeEmitterData, SettingFieldValue } from '../setting-tool.models';

@Component({
    selector: 'lib-setting-tool-switch',
    templateUrl: './setting-tool-switch.component.html',
    styleUrls: ['./setting-tool-switch.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettingToolSwitchComponent {
    @Input() label?: string;
    @Input() field: string;
    @Input() value: SettingFieldValue;
    @Input() disabled?: boolean;

    @Output() onChange = new EventEmitter<SettingFieldChangeEmitterData>();

    changeValue() {
        this.value.value = !this.value.value;
        this.onChange.emit({ field: this.field, value: this.value.value });
    }

    get classes() {
        let classes = 'my-slide';
        if (this.value?.isMixed) classes += ' my-slide--mixed';
        return classes;
    }
}
