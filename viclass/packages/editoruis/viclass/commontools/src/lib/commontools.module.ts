import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { Injector, NgModule } from '@angular/core';
import {
    COMMON_TOOLS_SETTINGS_TOKEN,
    CommonToolsModuleSettings,
    CONTEXT_MENU_TOOLS_SETTINGS_TOKEN,
    EMBED_TOOLS_SETTINGS_TOKEN,
    MODULE_SETTINGS_TOKEN,
    ZOOM_TOOLS_SETTINGS_TOKEN,
} from './commontools.model';
import { CommontoolsComponent } from './commontools/commontools.component';
import { ContextMenuComponent } from './contextmenu/contextmenu.component';
import { TooltipComponent } from './tooltip/tooltip.component';
import { ZoomtoolsComponent } from './zoomtools/zoomtools.component';

@NgModule({
    declarations: [CommontoolsComponent, ZoomtoolsComponent, ContextMenuComponent],
    imports: [CommonModule, OverlayModule, TooltipComponent],
    exports: [CommontoolsComponent, ZoomtoolsComponent, ContextMenuComponent],
    providers: [
        {
            provide: MODULE_SETTINGS_TOKEN,
            useValue: new CommonToolsModuleSettings(),
        },
        {
            provide: COMMON_TOOLS_SETTINGS_TOKEN,
            useFactory: (moduleSettings: CommonToolsModuleSettings) => moduleSettings.commonToolsSettings,
            deps: [MODULE_SETTINGS_TOKEN],
        },
        {
            provide: ZOOM_TOOLS_SETTINGS_TOKEN,
            useFactory: (moduleSettings: CommonToolsModuleSettings) => moduleSettings.zoomToolsSettings,
            deps: [MODULE_SETTINGS_TOKEN],
        },
        {
            provide: EMBED_TOOLS_SETTINGS_TOKEN,
            useFactory: (moduleSettings: CommonToolsModuleSettings) => moduleSettings.embedToolsSettings,
            deps: [MODULE_SETTINGS_TOKEN],
        },
        {
            provide: CONTEXT_MENU_TOOLS_SETTINGS_TOKEN,
            useFactory: (moduleSettings: CommonToolsModuleSettings) => moduleSettings.contextMenuToolsSettings,
            deps: [MODULE_SETTINGS_TOKEN],
        },
    ],
})
export class CommontoolsModule {
    constructor(private injector: Injector) {}
}
