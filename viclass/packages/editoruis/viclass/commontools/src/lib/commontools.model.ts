import { InjectionToken } from '@angular/core';
import { EditorType } from '@viclass/editor.core';
import { ComponentSettings } from '@viclass/editorui.loader';

export const MODULE_SETTINGS_TOKEN = new InjectionToken<CommonToolsModuleSettings>(
    'common-tools-module-settings-token'
);
export const COMMON_TOOLS_SETTINGS_TOKEN = new InjectionToken<CommonToolsSettings>('common-tools-settings-token');

export const ZOOM_TOOLS_SETTINGS_TOKEN = new InjectionToken<ZoomToolsSettings>('zoom-tools-settings-token');

export const CONTEXT_MENU_TOOLS_SETTINGS_TOKEN = new InjectionToken<ContextMenuToolsSettings>(
    'context-menu-tools-settings-token'
);
export const EMBED_TOOLS_SETTINGS_TOKEN = new InjectionToken<ZoomToolsSettings>('embed-tools-settings-token');

export class CommonToolsSettings implements ComponentSettings {
    availableEditors: EditorType[] = [];
    iconClasses: { [key in EditorType]: string };
}

export class ZoomToolsSettings implements ComponentSettings {
    // all settings for zoom tools will be defined here
    sample: string = 'sample-setting';
}

export class EmbedToolsSettings implements ComponentSettings {
    // all settings for embed tools will be defined here
    sample: string = 'sample-setting';
}

export class ContextMenuToolsSettings implements ComponentSettings {
    // all settings for zoom tools will be defined here
    sample: string = 'sample-setting';
}

export class CommonToolsModuleSettings {
    // default settings
    commonToolsSettings: CommonToolsSettings = new CommonToolsSettings();
    zoomToolsSettings: ZoomToolsSettings = new ZoomToolsSettings();
    embedToolsSettings: EmbedToolsSettings = new EmbedToolsSettings();
    contextMenuToolsSettings: ContextMenuToolsSettings = new ContextMenuToolsSettings();
}
