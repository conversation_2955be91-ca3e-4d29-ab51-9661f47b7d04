import { ComponentRef, createNgModule, Injector, ViewContainerRef } from '@angular/core';
import { EditorUIComponent } from '@viclass/editorui.loader';
import { FreedrawingtoolsModule } from './freedrawingtools.module';
import { FreedrawingtoolsComponent } from './freedrawingtools.component';

export function factory(
    viewContainerRef: ViewContainerRef,
    injector: Injector
): Promise<ComponentRef<EditorUIComponent>> {
    const module = createNgModule(FreedrawingtoolsModule, injector);
    return new Promise((rs, rj) => {
        const t = module.injector.runInContext(() =>
            viewContainerRef.createComponent(FreedrawingtoolsComponent, {
                environmentInjector: module,
                injector: injector,
            })
        );
        rs(t);
    });
}
