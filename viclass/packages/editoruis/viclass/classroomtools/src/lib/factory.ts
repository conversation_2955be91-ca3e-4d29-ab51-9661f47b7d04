import { ComponentRef, createNgModule, Injector, ViewContainerRef } from '@angular/core';
import { EditorUIComponent } from '@viclass/editorui.loader';
import { MODULE_SETTINGS_TOKEN } from './classroomtools.model';
import { ClassroomToolsModule } from './classroomtools.module';
import { ClassroomToolsComponent } from './classroomtools/classroomtools.component';

export function factoryClassroom(
    viewContainerRef: ViewContainerRef,
    injector: Injector,
    settings?: any
): Promise<ComponentRef<EditorUIComponent>> {
    const module = createNgModule(ClassroomToolsModule, injector);

    if (settings) {
        const moduleSettings = module.injector.get(MODULE_SETTINGS_TOKEN);
        moduleSettings.classroomToolsSettings = settings;
    }

    return new Promise((rs, rj) => {
        rs(
            module.injector.runInContext(() =>
                viewContainerRef.createComponent(ClassroomToolsComponent, {
                    injector: injector,
                    ngModuleRef: module,
                })
            )
        );
    });
}
