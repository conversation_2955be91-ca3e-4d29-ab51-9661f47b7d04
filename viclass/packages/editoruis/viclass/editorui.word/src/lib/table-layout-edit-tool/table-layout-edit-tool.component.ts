import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { TableContextState, WordTableTool } from '@viclass/editor.word';
import { ButtonData, TableActions } from '../wordtools.models';

const VerticalAlignValue = {
    VerticalAlignTop: 'top',
    VerticalAlignMiddle: 'middle',
    VerticalAlignBottom: 'bottom',
} as const;

@Component({
    selector: 'lib-table-layout-edit-tool',
    templateUrl: './table-layout-edit-tool.component.html',
    styleUrls: ['./table-layout-edit-tool.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TableLayoutEditToolComponent {
    @Input()
    tableContext: TableContextState;
    @Input()
    tableTool: WordTableTool;

    @Output()
    onClose = new EventEmitter<void>();

    tableMergeSplitBtn: ButtonData<TableActions>[] = [
        {
            name: 'MergeCells',
            iconClasses: 'vcon vcon-word vcon_table_merge',
            label: 'Gộp ô',
        },
        {
            name: 'SplitCell',
            iconClasses: 'vcon vcon-word vcon_table_split',
            label: 'Tách ô',
        },
    ];

    tableDistributeBtn: ButtonData<TableActions>[] = [
        {
            name: 'DistributeRowsEvenly',
            iconClasses: 'vcon vcon-word vcon_distribute-rows',
            label: 'Giãn dòng tự động',
        },
        {
            name: 'DistributeColumnsEvenly',
            iconClasses: 'vcon vcon-word vcon_distribute-cols',
            label: 'Giãn cột tự động',
        },
    ];

    tableInsertActionBtns: ButtonData<TableActions>[] = [
        {
            name: 'InsertRowAbove',
            iconClasses: 'vcon vcon-word vcon_table_row-above-ad',
            label: 'Chèn hàng phía trên',
        },
        {
            name: 'InsertRowBelow',
            iconClasses: 'vcon vcon-word vcon_table_row-below-ad',
            label: 'Chèn hàng phía dưới',
        },
        {
            name: 'InsertColumnLeft',
            iconClasses: 'vcon vcon-word vcon_table_column-left-ad',
            label: 'Chèn cột bên trái',
        },
        {
            name: 'InsertColumnRight',
            iconClasses: 'vcon vcon-word vcon_table_column-right-ad',
            label: 'Chèn cột bên phải',
        },
    ];

    tableDeleteActionBtns: ButtonData<TableActions>[] = [
        {
            name: 'DeleteRow',
            iconClasses: 'vcon vcon-word vcon_delete-row',
            label: 'Xóa hàng',
        },
        {
            name: 'DeleteColumn',
            iconClasses: 'vcon vcon-word vcon_delete-column',
            label: 'Xóa cột',
        },
        {
            name: 'DeleteTable',
            iconClasses: 'vcon vcon-word vcon_table_delete-table',
            label: 'Xóa bảng',
        },
    ];

    tableCellVerticalAlignBtns: ButtonData<TableActions>[] = [
        {
            name: 'VerticalAlignTop',
            iconClasses: 'vcon vcon-word vcon_text-align_top',
            label: 'Căn góc trên',
        },
        {
            name: 'VerticalAlignMiddle',
            iconClasses: 'vcon vcon-word vcon_text-align_middle',
            label: 'Căn giữa',
        },
        {
            name: 'VerticalAlignBottom',
            iconClasses: 'vcon vcon-word vcon_text-align_bottom',
            label: 'Căn góc dưới',
        },
    ];

    /**
     * Check if the vertical align format of the cell is active
     */
    isVerticalCellActive = (toolName: string): boolean => {
        const currValue = this.tableContext?.currentCellVerticalAlign || VerticalAlignValue.VerticalAlignTop;
        return currValue === VerticalAlignValue[toolName];
    };

    get tableInsertDeleteBtns() {
        return [...this.tableInsertActionBtns, ...this.tableDeleteActionBtns];
    }

    /**
     * Check if specific action is allowed based on table context
     */
    isActionAllowed(action: string): boolean {
        switch (action) {
            case 'merge':
                return this.tableContext?.canMerge || false;
            case 'unmerge':
                return this.tableContext?.canUnmerge || false;
            default:
                return true;
        }
    }

    /**
     * Handle table click event on the action buttons.
     * Includes insert/delete/merge/align... on the current table selection
     */
    onTableActionBtnClick(btn: ButtonData<TableActions>) {
        switch (btn.name) {
            case 'InsertRowAbove':
                this.tableTool.insertRow(false);
                break;
            case 'InsertRowBelow':
                this.tableTool.insertRow(true);
                break;
            case 'InsertColumnLeft':
                this.tableTool.insertColumn(false);
                break;
            case 'InsertColumnRight':
                this.tableTool.insertColumn(true);
                break;
            case 'DeleteRow':
                this.tableTool.deleteRow();
                break;
            case 'DeleteColumn':
                this.tableTool.deleteColumn();
                break;
            case 'DeleteTable':
                this.tableTool.deleteTable();
                break;
            case 'MergeCells':
                this.tableTool.mergeCells();
                break;
            case 'SplitCell':
                this.tableTool.unmergeCells();
                break;
            case 'VerticalAlignTop':
            case 'VerticalAlignMiddle':
            case 'VerticalAlignBottom':
                this.tableTool.changeCellVerticalAlign(VerticalAlignValue[btn.name]);
                break;
            case 'DistributeColumnsEvenly':
                this.tableTool.distributeColumnsEvenly();
                break;
            case 'DistributeRowsEvenly':
                this.tableTool.distributeRowsEvenly();
                break;
            default:
                return;
        }
    }

    /**
     * Check if the table action is enable. E.g. you can only MergeCells when select multiple cells
     */
    isTableActionEnable = (action: TableActions): boolean => {
        const tableState = this.tableContext;
        if (!tableState || (!tableState.isTableEditing && !tableState.isTableSelection)) return false;

        switch (action) {
            case 'MergeCells':
                return tableState.canMerge;
            case 'SplitCell':
                return tableState.canUnmerge;
            case 'DistributeColumnsEvenly':
                return tableState.selectionCounts.columns > 1;
            case 'DistributeRowsEvenly':
                return tableState.selectionCounts.rows > 1;
            default:
                return true;
        }
    };
}
