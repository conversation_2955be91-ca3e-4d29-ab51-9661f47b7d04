@import style

@import wordfonts

@tailwind components

@layer components
  @import output

.vcon-word.vcon_text_color1
  .path5
    &::before
      color: var(--color-text--) !important

.vcon-word.vcon_text_highlight-color1
  .path11
    &::before
      color: var(--color-highlight--) !important

.vcon-word.vcon_color-list
  .path2
    &:before
      color: var(--color--) !important

.vcon-word.vcon_brush-outline
  .path1
    &:before
      color: var(--color--) !important

.word-shadow-1
  box-shadow: 0px 5px 20px 0px rgba(0, 66, 75, 0.2) !important
.word-shadow-2
  box-shadow: 0px 0px 15px 0px rgba(219, 0, 255, 0.50) !important
.word-shadow-3
  box-shadow: 1px 1px 4px 0px rgba(0, 174, 239, 0.35) !important

.word-border-1
  border-width: 2px !important
  border-style: solid !important
  border-color: rgb(var(--BW1)) !important
.word-border-2
  border-width: 2px !important
  border-style: dashed !important
  border-color: rgb(var(--BW1)) !important
.word-border-3
  border-width: 7px !important
  border-style: double !important
  border-color: rgb(var(--BW1)) !important

.heading-wrapper-item
  position: relative

  .heading-wrapper-overlays
    visibility: hidden
    position: absolute
    top: 0
    bottom: 0
    left: 0
    right: 0
    width: 100%
    height: 100%

  &:hover
    .heading-wrapper-overlays
      visibility: visible

/* !NOTE: Please update the default style config in `HeadingWrapperPlugin.ts` when you change this */
.v-toolbar .HeadingWrapper
  color: '#121414'

  &.HeadingWrapper__normal-1
    font-family: 'Montserrat'
    font-weight: 500
    font-size: 14px

  &.HeadingWrapper__normal-2
    font-family: 'Noto Sans'
    font-weight: 400
    font-size: 14px

  &.HeadingWrapper__title-1
    font-family: 'Time New Roman'
    font-weight: 700
    font-size: 20px

  &.HeadingWrapper__title-2
    font-family: 'Time New Roman'
    font-weight: 700
    font-size: 32px

  &.HeadingWrapper__title-3
    font-family: 'Pacifico'
    font-weight: 400
    font-size: 24px

  &.HeadingWrapper__title-4
    font-family: 'Pacifico'
    font-weight: 400
    font-size: 36px

/***** from https://range-input-css.netlify.app/ *****/
/*********** Baseline, reset styles ***********/
.v-tool-silder-thumb
  background: transparent
  cursor: pointer

/* Removes default focus */
.v-tool-silder-thumb:focus
  outline: none

/******** Chrome, Safari, Opera and Edge Chromium styles ********/
/* slider track */
.v-tool-silder-thumb::-webkit-slider-runnable-track
  background-color: rgb(var(--P2))
  border-radius: 0.5rem
  height: 0.5rem

/* slider thumb */
.v-tool-silder-thumb::-webkit-slider-thumb
  -webkit-appearance: none /* Override default look */
  appearance: none
  margin-top: -4px /* Centers thumb on the track */
  background-color: rgb(var(--P1))
  border-radius: 0.5rem
  height: 1rem
  width: 1rem

.v-tool-silder-thumb:focus::-webkit-slider-thumb
  outline: 3px solid rgb(var(--P1))
  outline-offset: 0.125rem

/*********** Firefox styles ***********/
/* slider track */
.v-tool-silder-thumb::-moz-range-track
  background-color: rgb(var(--P2))
  border-radius: 0.5rem
  height: 0.5rem

/* slider thumb */
.v-tool-silder-thumb::-moz-range-thumb
  background-color: rgb(var(--P1))
  border: none /*Removes extra border that FF applies*/
  border-radius: 0.5rem
  height: 1rem
  width: 1rem

.v-tool-silder-thumb:focus::-moz-range-thumb
  outline: 3px solid rgb(var(--P1))
  outline-offset: 0.125rem
