import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { CreateRegularPolygonTool, GeometryToolBar, GeometryToolType } from '@viclass/editor.geo';
import { BehaviorSubject, Subscription } from 'rxjs';
import { TOOLBAR, TOOLTYPE } from '../injection.token';
import { ToolListener, ToolListenerHost } from '../tool.listener';

/**
 * Component for display and edit the number of edges for CreateRegularPolygonTool
 */
@Component({
    selector: 'tb-regular-polygon-edges-input',
    templateUrl: './regular-polygon-edges-input.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegularPolygonEdgesInputComponent implements OnInit, OnDestroy, ToolListenerHost {
    private toolClickSubscription: Subscription;
    private toolListener: ToolListener;

    readonly noEdge$ = new BehaviorSubject<string>('');
    readonly isInputValid$ = new BehaviorSubject<boolean>(true);
    readonly selectingPointState$ = new BehaviorSubject<number>(-1);

    public eoc$ = new BehaviorSubject<number>(0);
    tool: CreateRegularPolygonTool;

    constructor(
        @Inject(TOOLBAR) private toolBar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType,
        changeDetectorRef: ChangeDetectorRef
    ) {
        this.toolListener = new ToolListener(this, tooltype, changeDetectorRef);
        this.tool = this.toolBar.getTool(this.tooltype) as CreateRegularPolygonTool;
    }

    ngOnInit(): void {
        this.toolBar.registerToolListener(this.toolListener);

        this.updateInputFromToolState();
    }

    ngOnDestroy(): void {
        this.toolClickSubscription?.unsubscribe();
        this.toolBar.unregisterToolListener(this.toolListener);
    }

    setNoEdge(event: any) {
        if (this.isValidEdge(parseInt(event.target.value))) {
            const toolState = this.tool.toolState;

            toolState.noEdge = parseInt(event.target.value);
            this.tool.refreshToolState();
            this.isInputValid$.next(true);
        } else {
            this.isInputValid$.next(false);
        }
    }

    selectNextPoint() {
        this.tool.rotateNextVert();
        this.tool.refreshToolState();
    }

    setEOC(value: number) {
        const toolState = this.tool.toolState;

        toolState.drawMode = value;
        this.tool.refreshToolState();
    }

    get noEdge(): number {
        return this.tool.toolState.noEdge;
    }

    /**
     * Check if the number of edges is valid (an integer within the allowed range)
     */
    isValidEdge(noEdge: number) {
        return (
            Number.isSafeInteger(noEdge) &&
            noEdge >= CreateRegularPolygonTool.EDGE_MIN &&
            noEdge <= CreateRegularPolygonTool.EDGE_MAX
        );
    }

    /**
     * TODO, validation at UI is enough.
     * Another validation should be done when construction on server.
     * Communicating between tool and UI for range constraint is overkill
     */
    updateInputFromToolState() {
        const ts = this.tool.toolState;
        this.noEdge$.next(ts.noEdge.toString());
        this.selectingPointState$.next(ts.selectedExistingPoints);
        this.eoc$.next(ts.drawMode);
    }
}
