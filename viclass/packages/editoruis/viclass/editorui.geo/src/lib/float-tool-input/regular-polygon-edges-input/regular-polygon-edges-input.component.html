<div class="regpolygon-edge-input-overlay shadow-SH1 gap-1">
    <div class="flex flex-column justify-center items-center gap-2">
        <div class="flex justify-center items-center gap-[20px]" *ngIf="(selectingPointState$ | async) < 1">
            <button
                type="button"
                class="vi-btn vi-btn-small"
                [ngClass]="{ 'vi-btn-focus': (eoc$ | async) == 2 }"
                (click)="setEOC(2)">
                Center-Mid
            </button>
            <button
                type="button"
                class="vi-btn vi-btn-small"
                [ngClass]="{ 'vi-btn-focus': (eoc$ | async) == 1 }"
                (click)="setEOC(1)">
                Center
            </button>
            <button
                type="button"
                class="vi-btn vi-btn-small"
                [ngClass]="{ 'vi-btn-focus': (eoc$ | async) == 0 }"
                (click)="setEOC(0)">
                Edge
            </button>
        </div>
        <div class="flex justify-center items-center gap-[20px]" *ngIf="(selectingPointState$ | async) < 1">
            <label for="regpolygon-edge-input-overlay__input">
                Số cạnh
                <input
                    type="number"
                    id="regpolygon-edge-input-overlay__input"
                    min="3"
                    max="10"
                    [value]="noEdge$ | async"
                    (input)="setNoEdge($event)"
                    class="form-control"
                    [ngClass]="{
                        invalid: !(isInputValid$ | async),
                    }" />
            </label>
            <ng-template [ngIf]="(eoc$ | async) == 0">
                <button type="button" class="vi-btn vi-btn-small vi-btn-focus" (click)="selectNextPoint()">Xoay</button>
            </ng-template>
        </div>
        <div class="text-center text-[10px]" *ngIf="(selectingPointState$ | async) >= 0">
            Chọn thêm điểm đã có cho đa giác đều. Click ra ngoài để hoàn thành. Đã chọn thêm:
            {{ selectingPointState$ | async }}
        </div>
    </div>
    <div *ngIf="!(isInputValid$ | async)" class="text-SC5 text-center text-[10px]">Giá trị hợp lệ phải từ 3 đến 10</div>
</div>
