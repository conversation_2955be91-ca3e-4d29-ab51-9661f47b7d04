import { Injectable } from '@angular/core';
import { GeometryToolType } from '@viclass/editor.geo';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { ButtonData } from './geometrytools.models';

@Injectable({
    providedIn: 'root',
})
export class GeoToolbarBtnStateService {
    /**
     * Keep track of the path that the mouse is hovering over
     */
    public readonly focusPath$ = new BehaviorSubject<string[]>([]);
    private _pinnedPath: string = '';
    private leaveTimeoutHandle?: number;

    constructor() {}

    enterPath(path: string) {
        this.clearLastLeaveTimeout();
        // new paths should contains only the parent paths and the newly entered path
        const newPaths = this.focusPath$.value.filter(old => path.startsWith(old) && old !== path);
        newPaths.push(path);
        this.focusPath$.next(newPaths);
    }

    pinPath(path: string) {
        this._pinnedPath = path;
    }

    leavePath(path: string, event?: PointerEvent) {
        this.clearLastLeaveTimeout();

        this.leaveTimeoutHandle = window.setTimeout(
            () => {
                this.focusPath$.next(this.focusPath$.value.filter(p => !p.startsWith(path)));
                this.leaveTimeoutHandle = undefined;
            },
            this.pointerLeaveTimeoutMs(path, event)
        );
    }

    clearPath() {
        this.focusPath$.next([]);
        this._pinnedPath = '';
    }

    isFocusPath(path: string): Observable<boolean> {
        return this.focusPath$.pipe(map(currentPaths => currentPaths.some(p => p.startsWith(path))));
    }

    private pointerLeaveTimeoutMs(path: string, event?: PointerEvent): number {
        return this._pinnedPath && path.startsWith(this._pinnedPath)
            ? 15_000
            : event?.pointerType === 'touch'
              ? 6000
              : 3000;
    }

    private clearLastLeaveTimeout() {
        if (this.leaveTimeoutHandle) window.clearTimeout(this.leaveTimeoutHandle);
    }

    /*
     * Add a global click listener to clear the focus path when clicking outside the toolbar
     */
    addGlobalClickHandler(): void {
        // Do this in capture phase to ensure it runs before any other click handlers
        document.addEventListener('click', this.onDocumentClick, true);
    }

    /*
     * Remove the global click listener
     */
    removeGlobalClickHandler(): void {
        document.removeEventListener('click', this.onDocumentClick, true);
    }

    /**
     * Handle document click to clear the focus path if clicked outside the toolbar
     */
    private onDocumentClick = (event: MouseEvent): void => {
        if (this.focusPath$.value.length === 0) return;

        const target = event.target as HTMLElement;
        if (!target?.matches('.geo-toolbar *')) this.clearPath();
    };

    getInitialGeometryButtonsDataForCommandTool(): ButtonData<GeometryToolType>[] {
        return [
            {
                name: 'InputCommandTool',
                iconClasses: 'vcon vcon-geometry vcon_input-text',
                label: 'Nhập công thức',
                children: [
                    {
                        name: 'InputCommandTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_circle',
                        key: 'Circle',
                        label: 'Đường tròn',
                        viName: 'Đường tròn',
                        ignoreShowInToolbar: true,
                    },
                    {
                        name: 'InputCommandTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_line',
                        key: 'LineVi',
                        label: 'Đường thẳng',
                        viName: 'Đường thẳng',
                        ignoreShowInToolbar: true,
                    },
                    {
                        name: 'InputCommandTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_segment',
                        key: 'LineSegment',
                        label: 'Đoạn thẳng',
                        viName: 'Đoạn thẳng',
                        ignoreShowInToolbar: true,
                    },
                    {
                        name: 'InputCommandTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_point',
                        key: 'Point',
                        label: 'Điểm',
                        viName: 'Điểm',
                        ignoreShowInToolbar: true,
                    },
                ],
            },
        ];
    }

    getInitialGeometryButtonsData(): ButtonData<GeometryToolType>[] {
        return [
            {
                name: 'CreatePointTool',
                iconClasses: 'vcon vcon-geometry vcon_geometry_point',
                label: 'Công cụ điểm',
                children: [
                    {
                        name: 'CreatePointTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_point',
                        label: 'Điểm mới (Ctrl+)',
                    },
                    {
                        name: 'PointOnObjectTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_point_on-object',
                        label: 'Điểm thuộc đối tượng (Ctrl+)',
                    },
                    {
                        name: 'IntersectionPointTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_point_intersect',
                        label: 'Giao điểm (Ctrl+)',
                    },
                    {
                        name: 'MiddlePointTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_point_midpoint',
                        label: 'Trung điểm / tâm (Ctrl+)',
                    },
                ],
            },
            {
                name: 'CreateLineTool',
                iconClasses: 'vcon vcon-geometry vcon_geometry_line',
                children: [
                    {
                        name: 'CreateLineTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_line',
                        label: 'Đường thẳng',
                    },
                    {
                        name: 'CreateLineSegmentTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_segment',
                        label: 'Đoạn thẳng',
                    },
                    {
                        name: 'CreateRayTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_ray',
                        label: 'Tia',
                    },
                    {
                        name: 'CreateVectorTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_vector',
                        label: 'Vector',
                    },
                    {
                        name: 'CreatePerpendicularLineTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_perpendicular-line',
                        label: 'Vẽ vuông góc',
                    },
                    {
                        name: 'CreateParallelLineTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_parallet-line',
                        label: 'Vẽ song song',
                    },
                    {
                        name: 'CreateBisectorLineTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_angle-bisector',
                        label: 'Vẽ đường phân giác',
                    },
                ],
            },
            {
                name: 'CreateSquareTool',
                iconClasses: 'vcon vcon-geometry vcon_geometry_squares',
                children: [
                    {
                        name: 'CreateSquareTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_squares',
                        label: 'Hình vuông',
                    },
                    {
                        name: 'CreateRectangleTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_rectangle',
                        label: 'Hình chữ nhật',
                    },
                    {
                        name: 'CreateParallelogramTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_parallelogram',
                        label: 'Hình bình hành',
                    },
                    {
                        name: 'CreateTrapezoidTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_trapezoid',
                        label: 'Hình thang',
                    },
                    {
                        name: 'CreateRhombusTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_diamond',
                        label: 'Hình thoi',
                    },
                ],
            },
            {
                name: 'CreateEllipseTool',
                iconClasses: 'vcon vcon-geometry vcon_geometry_ellipse',
                children: [
                    {
                        name: 'CreateEllipseTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_ellipse',
                        label: 'Hình elip',
                    },
                    {
                        name: 'CreateCircleTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_circle',
                        label: 'Hình tròn',
                    },
                    {
                        name: 'CreateSemicircleTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_circle_crescent',
                        label: 'Hình bán nguyệt',
                    },
                    {
                        name: 'CreateSectorTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_circle_a-quarter',
                        label: 'Hình quạt',
                    },
                ],
            },
            {
                name: 'CreateTriangleTool',
                iconClasses: 'vcon vcon-geometry vcon_geometry_triangle_scalene',
                children: [
                    {
                        name: 'CreateTriangleTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_triangle_scalene',
                        label: 'Tam giác thường',
                    },
                    {
                        name: 'CreateIsoscelesTriangleTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_triangle_isosceles',
                        label: 'Tam giác cân',
                    },
                    {
                        name: 'CreateEquilateralTriangleTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_triangle_equilateral',
                        label: 'Tam giác đều',
                    },
                    {
                        name: 'CreateRightTriangleTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_triangle_right',
                        label: 'Tam giác vuông',
                    },
                    {
                        name: 'CreateIsoscelesRightTriangleTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_triangle_right-angle',
                        label: 'Tam giác vuông cân',
                    },
                ],
            },
            {
                name: 'CreatePolygonTool',
                iconClasses: 'vcon vcon-geometry vcon_geometry_polygon',
                children: [
                    {
                        name: 'CreatePolygonTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_polygon',
                        label: 'Đa giác',
                    },
                    {
                        name: 'CreateRegularPolygonTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_polygon_regular',
                        label: 'Đa giác đều',
                    },
                ],
            },
            {
                name: 'CreateAngleByThreePointsTool',
                iconClasses: 'vcon vcon-geometry vcon_geometry_angle',
                children: [
                    {
                        name: 'CreateAngleByThreePointsTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_angle',
                        label: 'Vẽ góc từ 3 điểm',
                    },
                    {
                        name: 'CreateAngleTool',
                        iconClasses: 'vcon vcon-geometry vcon_geometry_angle2',
                        label: 'Vẽ góc từ 2 đường thẳng',
                    },
                ],
            },
            {
                name: 'CreateSymmetricThroughPointTool',
                iconClasses: 'vcon vcon-geometry vcon_reflect-by-point',
                children: [
                    {
                        name: 'CreateSymmetricThroughPointTool',
                        iconClasses: 'vcon vcon-geometry vcon_reflect-by-point',
                        label: 'Vẽ đối xứng qua điểm',
                    },
                    {
                        name: 'CreateSymmetricThroughLineTool',
                        iconClasses: 'vcon vcon-geometry vcon_reflect-by-line',
                        label: 'Vẽ đối xứng qua đường thẳng',
                    },
                ],
            },
        ];
    }
}
