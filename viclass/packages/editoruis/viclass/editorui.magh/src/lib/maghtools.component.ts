import { ConnectionPositionPair, HorizontalConnectionPos, VerticalConnectionPos } from '@angular/cdk/overlay';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {
    DefaultToolBar,
    FEATURE_SELECTION,
    ToolEventListener,
    VEventListener,
    ViewportContentEvent,
} from '@viclass/editor.core';
import {
    EquationsState,
    MaghZoomToolState,
    MathGraphSettingsState,
    MathGraphSettingsTool,
    MathGraphTool,
    MathGraphToolBar,
    MathGraphToolEventData,
    MathGraphToolType,
    MathGraphZoomTool,
    UpdateEquationsTool,
} from '@viclass/editor.magh';
import { EditorUIComponent, EditorUILoaderComponent } from '@viclass/editorui.loader';
import { initFlowbite, TooltipOptions } from 'flowbite';
import { BehaviorSubject, combineLatest, map, Observable, ReplaySubject, Subscription } from 'rxjs';
import { MaghEquationService } from './magh.equation.service';
import { MathGraphToolbarBtnStateService } from './maghtoolbar-btn-state.service';
import { CommunicationEvent, MathGraphEditorControllerEvent, MathGraphTools, RenderPropsCtx } from './maghtools.models';
import { PlotDialogComponent } from './plot-dialog/plot-dialog.component';
import { SettingFieldChangeEmitterData } from './setting-tool/setting-tool.models';

@Component({
    changeDetection: ChangeDetectionStrategy.OnPush,
    selector: 'tb-maghtools',
    templateUrl: './maghtools.component.html',
})
export class MathGraphtoolsComponent implements OnInit, OnDestroy, EditorUIComponent {
    show = false;

    private notifier: ReplaySubject<CommunicationEvent<MathGraphEditorControllerEvent>>;
    private readonly editor$: BehaviorSubject<MathGraphTools> = new BehaviorSubject(null);
    equationsContext$: BehaviorSubject<EquationsState> = new BehaviorSubject(null);

    settingsContext$: BehaviorSubject<MathGraphSettingsState> = new BehaviorSubject(null);
    zoomContext$: BehaviorSubject<MaghZoomToolState> = new BehaviorSubject(null);

    renderPropsCtx$: Observable<RenderPropsCtx> = combineLatest([this.settingsContext$, this.zoomContext$]).pipe(
        map(([settings, zoom]) => ({
            ...settings,
            scale: zoom === null ? null : Math.round(zoom.zoomLevel * 100),
        }))
    );

    contentEventListener: VEventListener<ViewportContentEvent>;

    private readonly subscription: Subscription;

    vAlign: 'top' | 'center' | 'bottom';
    hAlign: 'left' | 'center' | 'right';
    direction: 'ltr' | 'rtl' | 'btt' | 'ttb';

    listBtnChildren: {
        name: MathGraphToolType;
        param: string;
        iconClasses: string;
    }[] = [];

    constructor(
        public dialog: MatDialog,
        private changeDetectorRef: ChangeDetectorRef,
        private btnStateSv: MathGraphToolbarBtnStateService,
        private maghEquationService: MaghEquationService
    ) {
        this.notifier = new ReplaySubject<CommunicationEvent<MathGraphEditorControllerEvent>>();
        this.subscription = this.notifier.subscribe(event => this.handleEvents(event));
    }

    ngOnInit(): void {
        initFlowbite();
        this.editor$.subscribe(tools => {
            if (!tools) return;
            tools.toolbar.registerToolListener(MathGraphtoolsComponent.MathGraphToolListener(this));
        });
        this.btnStateSv.addGlobalClickHandler();
    }

    ngOnDestroy(): void {
        this.subscription?.unsubscribe();
        this.btnStateSv.removeGlobalClickHandler();
    }

    private get tools(): MathGraphTools {
        return this.editor$.getValue();
    }

    private get toolBar(): MathGraphToolBar {
        return this.tools?.toolbar;
    }

    private getTool(tool: MathGraphToolType): MathGraphTool<any> {
        return this.toolBar.getTool(tool);
    }

    get updateTool(): UpdateEquationsTool {
        return this.getTool('UpdateEquationsTool') as UpdateEquationsTool;
    }

    get settingTool(): MathGraphSettingsTool {
        return this.getTool('MaghSettingsTool') as MathGraphSettingsTool;
    }

    get zoomTool(): MathGraphZoomTool {
        return this.getTool('MaghZoomTool') as MathGraphZoomTool;
    }

    private get viewport(): string {
        return this.toolBar.viewport?.id;
    }

    get isHorizontal(): boolean {
        return this.direction === 'ltr' || this.direction === 'rtl';
    }

    get isVertical(): boolean {
        return this.direction === 'ttb' || this.direction === 'btt';
    }

    get subMenuPositions(): ConnectionPositionPair[] {
        return this.createSubMenuPositions();
    }

    get equationsSubMenuPositions(): ConnectionPositionPair[] {
        return this.createSubMenuPositions(225, -20);
    }

    get settingsSubMenuPositions(): ConnectionPositionPair[] {
        return this.createSubMenuPositions(180, -20);
    }

    // get colorList(): string[] {
    //     return ['#FFFFFF', '#121414', '#00AEEF', '#DB00FF', '#31E37C', '#FFD600', '#FF7A00', '#FF002E'];
    // }

    onMouseEnter(btnType: string) {
        this.btnStateSv.enterPath(btnType);
    }

    onMouseLeave(btnType: string, event: PointerEvent) {
        this.btnStateSv.leavePath(btnType, event);
    }

    connectToolbar<T extends DefaultToolBar<any, any>>(toolbar: T) {
        if (!toolbar) return;
        this.editor$.next(new MathGraphTools(toolbar as unknown as MathGraphToolBar));

        if (!this.contentEventListener && toolbar.viewport) {
            // meaning the toolbar is attached to viewport before connecting to UI
            this.contentEventListener = new MathGraphtoolsComponent._contentEventListener(this);
            toolbar.viewport.contentEventEmitter.registerListener(this.contentEventListener);
        }
    }

    hasTool(tool: MathGraphToolType) {
        return this.toolBar && this.toolBar.getTool(tool) != null;
    }

    switchTool(toolType: MathGraphToolType) {
        this.notifier.next({
            source: this,
            eventType: 'switch-tool',
            eventData: toolType,
        });
    }

    isToolActive = (toolType: MathGraphToolType, param?: string): boolean => {
        return this.toolBar.isToolActive(toolType);
    };

    isToolEnable = (toolType: MathGraphToolType): boolean => {
        if (toolType === 'CreateMathGraphDocumentTool') {
            return !this.toolBar.isToolDisable('CreateMathGraphDocumentTool');
        }

        return !!this.getTool(toolType)?.focusAble(this.viewport);
    };

    disableUI() {}

    hideUI() {
        this.toolBar?.clearAllFocus();
        this.show = false;
        this.changeDetectorRef.markForCheck();
    }

    showUI() {
        this.show = true;
        this.changeDetectorRef.markForCheck();
    }

    isShowing(): boolean {
        return this.show;
    }

    loadedBy(uiLoader: EditorUILoaderComponent) {
        this.vAlign = uiLoader.vAlign;
        this.hAlign = uiLoader.hAlign;
        this.direction = uiLoader.direction;
    }

    private handleEvents(event: CommunicationEvent<MathGraphEditorControllerEvent>) {
        switch (event.eventType) {
            case 'switch-tool': {
                const toolType = event.eventData as MathGraphToolType;

                if (!this.toolBar.isToolActive(toolType)) this.toolBar.focus(toolType);
                else this.toolBar.blur(toolType);

                break;
            }
            default:
                break;
        }
    }

    showTooltip(tooltipId: string) {
        const Tooltip = window['Tooltip'];
        const flowbiteInstances = window['FlowbiteInstances'];

        const Default: TooltipOptions = {
            placement: 'top',
            triggerType: 'hover',
            onShow: () => {},
            onHide: () => {},
            onToggle: () => {},
        };

        const $triggerEl = document.querySelector(`[data-tooltip-target=${tooltipId}]`);
        const $tooltipEl: any = document.getElementById(tooltipId);

        let tooltip = flowbiteInstances.getInstance('Tooltip', tooltipId);

        if (!$tooltipEl) {
            console.error(
                `The tooltip element with id "${tooltipId}" does not exist. Please check the data-tooltip-target attribute.`
            );
        } else if (!tooltip) {
            const triggerType = $triggerEl.getAttribute('data-tooltip-trigger');
            const placement = $triggerEl.getAttribute('data-tooltip-placement');

            tooltip = new Tooltip(
                $tooltipEl as HTMLElement,
                $triggerEl as HTMLElement,
                {
                    placement: placement ? placement : Default.placement,
                    triggerType: triggerType ? triggerType : Default.triggerType,
                } as TooltipOptions
            );
        }

        tooltip.show();
    }

    private createSubMenuPositions(mainOffset = 50, subOffset = 0): ConnectionPositionPair[] {
        const horizontalConnectionPos: HorizontalConnectionPos = 'center';
        const verticalConnectionPos: VerticalConnectionPos = 'center';

        let offsetX = 0;
        let offsetY = 0;

        if (this.isHorizontal) {
            if (this.vAlign === 'top') {
                offsetY = mainOffset;
                offsetX = subOffset;
            } else {
                offsetY = -mainOffset;
                offsetX = -subOffset;
            }
        } else if (this.isVertical) {
            if (this.hAlign === 'right') {
                offsetX = -mainOffset;
                offsetY = -subOffset;
            } else {
                offsetX = mainOffset;
                offsetY = subOffset;
            }
        }

        return [
            new ConnectionPositionPair(
                {
                    originX: horizontalConnectionPos,
                    originY: verticalConnectionPos,
                },
                {
                    overlayX: horizontalConnectionPos,
                    overlayY: verticalConnectionPos,
                },
                offsetX,
                offsetY
            ),
        ];
    }

    /**
     * Listen for content changes on viewport and check for changes on the UI
     */
    private static _contentEventListener = class implements VEventListener<ViewportContentEvent> {
        constructor(private cmp: MathGraphtoolsComponent) {}
        onEvent(eventData: ViewportContentEvent): ViewportContentEvent | Promise<ViewportContentEvent> {
            if (eventData.state && eventData.state.source === FEATURE_SELECTION) {
                if (this.cmp.isShowing()) this.cmp.changeDetectorRef.markForCheck();
            }

            return eventData;
        }
        onUnregister?: () => void;
    };

    private static MathGraphToolListener(
        _p: MathGraphtoolsComponent
    ): ToolEventListener<MathGraphToolBar, MathGraphToolType> {
        return new (class implements ToolEventListener<MathGraphToolBar, MathGraphToolType> {
            onEvent(eventData: MathGraphToolEventData): MathGraphToolEventData {
                switch (eventData.toolType) {
                    case 'UpdateEquationsTool': {
                        _p.updateEquationsContext();
                        break;
                    }
                    case 'MaghSettingsTool': {
                        _p.updateSettingsContext();
                        break;
                    }
                    case 'MaghZoomTool': {
                        _p.updateZoomContext();
                        break;
                    }
                }

                _p.changeDetectorRef.detectChanges();
                return eventData;
            }
        })();
    }

    updateEquationsContext() {
        const context = this.toolBar.toolState<EquationsState>('UpdateEquationsTool');
        if (!context) return;

        this.equationsContext$.next(context);
        this.maghEquationService.equationChanged$.next();
    }

    updateSettingsContext() {
        const context = this.toolBar.toolState<MathGraphSettingsState>('MaghSettingsTool');
        if (!context) return;

        this.settingsContext$.next(context);
        this.maghEquationService.equationChanged$.next();
    }

    updateZoomContext() {
        const context = this.toolBar.toolState<MaghZoomToolState>('MaghZoomTool');
        if (!context) return;

        this.zoomContext$.next(context);
    }

    onPlotBtnClicked() {
        this.dialog.open(PlotDialogComponent, {
            panelClass: 'plot-tool',
            data: { ...this.equationsContext$.value, tool: this.updateTool },
        });
    }

    onSettingFieldChange(ev: SettingFieldChangeEmitterData) {
        const settingsTool = this.getTool('MaghSettingsTool') as MathGraphSettingsTool;
        settingsTool.updateSetting({ [ev.field]: ev.value });
    }
}
