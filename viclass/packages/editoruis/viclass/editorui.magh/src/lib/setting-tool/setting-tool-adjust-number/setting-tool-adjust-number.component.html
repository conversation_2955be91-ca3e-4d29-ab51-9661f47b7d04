<div class="flex">
    <span *ngIf="label?.length" class="text-BW1">{{ label }}</span>
    <div class="flex-grow"></div>
    <div class="flex gap-3 items-center">
        <button
            [disabled]="disabled || _value <= min"
            (pointerdown)="changeValue(-step)"
            (pointerup)="cancelPointerPress()"
            (pointerleave)="cancelPointerPress()">
            <span
                class="vcon vcon-common vcon_page-bar_zoom-out"
                [ngClass]="{
                    'text-BW4': disabled || _value <= min,
                    '!cursor-default': disabled || _value <= min,
                }"></span>
        </button>
        <span *ngIf="!_isMixed">{{ _value }}{{ suffix ? suffix : '' }}</span>
        <span *ngIf="_isMixed">?</span>
        <button
            [disabled]="disabled || _value >= max"
            (pointerdown)="changeValue(step)"
            (pointerup)="cancelPointerPress()"
            (pointerleave)="cancelPointerPress()">
            <span
                class="vcon vcon-common vcon_page-bar_ad"
                [ngClass]="{
                    'text-BW4': disabled || _value >= max,
                    '!cursor-default': disabled || _value >= max,
                }"></span>
        </button>
    </div>
</div>
