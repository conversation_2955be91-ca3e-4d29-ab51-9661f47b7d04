<div class="v-toolbar math-toolbar with-sub" *ngIf="isShowing()">
    <div class="v-tool-group">
        <button
            class="v-tool-btn"
            placement="bottom"
            *ngIf="hasTool('CreateMathDocumentTool')"
            [ngClass]="{ active: isToolActive('CreateMathDocumentTool') }"
            (click)="switchTool('CreateMathDocumentTool')"
            [disabled]="!isToolEnable('CreateMathDocumentTool')">
            <span class="vcon vcon-mathtype vcon_page-bar_ad"></span>
        </button>

        <button
            #editLatexBtnEl
            class="v-tool-btn"
            placement="bottom"
            [disabled]="!isSingleDocFocused('InsertLatexTool') && mathContext$ | async"
            [ngClass]="{ active: openLatexTool$ | async }"
            (click)="onToggleLatexTool()">
            <span class="vcon vcon-mathtype vcon-latex"></span>
            <tooltip-math [toolTipFor]="editLatexBtnEl" [tooltipContent]="'Chỉnh sửa LaTeX'"></tooltip-math>
        </button>
        <ng-template
            *ngIf="mathContext$ | async as mathContext"
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="editLatexBtnEl"
            [cdkConnectedOverlayOpen]="openLatexTool$ | async"
            [cdkConnectedOverlayPositions]="createSubMenuPositions(175, 60)">
            <lib-latex-editor
                [currentLatex]="mathContext.latex || ''"
                (close)="onToggleLatexTool()"
                [disabled]="!isToolActive('InsertLatexTool')"
                [syntaxErrors]="mathContext.errors"
                (latexChanged)="insertLatexTool.setContent($event)"></lib-latex-editor>
        </ng-template>

        <button
            *ngIf="enableVirtualKeyboard"
            #virtualKeyboardBtnEl
            class="v-tool-btn"
            placement="bottom"
            [disabled]="!isToolEnable('VirtualKeyboardTool')"
            [ngClass]="{ active: (virtualKeyboardShowing$ | async) }"
            (click)="toggleVirtualKeyboard()">
            <span class="vcon vcon-mathtype vcon-keyboard"></span>
            <tooltip-math [toolTipFor]="virtualKeyboardBtnEl" [tooltipContent]="'Bàn phím ảo'"></tooltip-math>
        </button>

        <div class="v-tool-separation"></div>

        <tb-toolbar-button
            [data]="insertStructureBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(3, true)"
            childSize="large"
            [layoutCols]="3"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <tb-toolbar-button
            [data]="largeOperatorBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(3, true)"
            childSize="large"
            [layoutCols]="3"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <tb-toolbar-button
            [data]="dotsBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(3)"
            [layoutCols]="3"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <button
            #matrixBtnEl
            class="v-tool-btn"
            placement="bottom"
            [disabled]="!isToolEnable('InsertLatexTool')"
            (pointerenter)="onMouseEnter('Matrix')"
            (pointerleave)="onMouseLeave('Matrix', $event)">
            <span class="vcon vcon-mathtype vcon-group_structure-matrix"></span>
            <tooltip-math [toolTipFor]="matrixBtnEl" tooltipContent="Ma trận"></tooltip-math>
        </button>
        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="matrixBtnEl"
            [cdkConnectedOverlayOpen]="focusMatrix$ | async"
            [cdkConnectedOverlayPositions]="createSubMenuPositions(110)">
            <tb-matrix-size-selector
                (pointerenter)="onMouseEnter('Matrix')"
                (pointerleave)="onMouseLeave('Matrix', $event)"
                (selected)="insertMatrix($event)"></tb-matrix-size-selector>
        </ng-template>
        <tb-toolbar-button
            [data]="spacingBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(3, true)"
            childSize="large"
            [layoutCols]="3"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>

        <div class="v-tool-separation"></div>

        <tb-toolbar-button
            [data]="symbolsBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(3)"
            [layoutCols]="3"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <tb-toolbar-button
            [data]="inequalityBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(3)"
            [layoutCols]="3"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <tb-toolbar-button
            [data]="equalityBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(3)"
            [layoutCols]="3"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <tb-toolbar-button
            [data]="groupRelBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(3)"
            [layoutCols]="3"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <tb-toolbar-button
            [data]="binaryRelBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(3)"
            [layoutCols]="3"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>

        <tb-toolbar-button
            [data]="greekBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(2)"
            [layoutCols]="2"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)">
            <div class="flex items-center justify-center">
                <mat-slide-toggle
                    class="alphabet-switch"
                    [checked]="greekUppercase"
                    (toggleChange)="greekUppercase = !greekUppercase"></mat-slide-toggle>
            </div>
        </tb-toolbar-button>
        <tb-toolbar-button
            [data]="foreignBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(2)"
            [layoutCols]="2"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)">
            <div class="flex items-center justify-center">
                <mat-slide-toggle
                    class="alphabet-switch"
                    [checked]="foreignUppercase"
                    (toggleChange)="foreignUppercase = !foreignUppercase"></mat-slide-toggle>
            </div>
        </tb-toolbar-button>
        <tb-toolbar-button
            [data]="accentsBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(2)"
            [layoutCols]="2"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <tb-toolbar-button
            [data]="extPanelBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(2)"
            [layoutCols]="2"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <tb-toolbar-button
            [data]="trigBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(1, true)"
            [layoutCols]="1"
            [allowCheck]="isLatexAllow"
            childSize="long"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <tb-toolbar-button
            [data]="inverseTrigBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(1, true)"
            [layoutCols]="1"
            [allowCheck]="isLatexAllow"
            childSize="long"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <tb-toolbar-button
            [data]="logBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(1, true)"
            [layoutCols]="1"
            [allowCheck]="isLatexAllow"
            childSize="long"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <tb-toolbar-button
            [data]="limBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(1, true)"
            [layoutCols]="1"
            [allowCheck]="isLatexAllow"
            childSize="long"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <tb-toolbar-button
            [data]="functionBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(1, true)"
            [layoutCols]="1"
            [allowCheck]="isLatexAllow"
            childSize="long"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <tb-toolbar-button
            [data]="arrowsBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(3)"
            [layoutCols]="3"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>
        <tb-toolbar-button
            [data]="parenthesisBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(3)"
            [layoutCols]="3"
            [allowCheck]="isLatexAllow"
            (btnClicked)="onInsertLatex($event)"></tb-toolbar-button>

        <div class="v-tool-separation"></div>

        <tb-toolbar-button
            [data]="fontStyleBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(1)"
            [isToolActive]="isFontStyleActive"
            [allowCheck]="isStyleAllow"
            (btnClicked)="onChangeFontStyle($event)"></tb-toolbar-button>

        <tb-toolbar-button
            [data]="fontSizeBtns"
            [subMenuPositions]="createMultiColsSubmenuPosition(1)"
            [isToolActive]="isFontSizeActive"
            [allowCheck]="isStyleAllow"
            (btnClicked)="onChangeFontSize($event)"></tb-toolbar-button>

        <button
            class="v-tool-btn"
            placement="bottom"
            #colorBtnEl
            [disabled]="!isToolEnable('MathStyleTool')"
            (pointerenter)="onMouseEnter('Color')"
            (pointerleave)="onMouseLeave('Color', $event)">
            <span class="vcon vcon-mathtype vcon-color-list">
                <span class="path1"></span>
                <span class="path2" [style]="'--color--:' + (styleContext$ | async)?.color"></span>
            </span>
        </button>
        <tooltip-math [toolTipFor]="colorBtnEl" [tooltipContent]="'Màu chữ'"></tooltip-math>

        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="colorBtnEl"
            [cdkConnectedOverlayOpen]="focusColor$ | async"
            [cdkConnectedOverlayPositions]="subMenuPositions">
            <div
                class="v-toolbar math-toolbar"
                (pointerenter)="onMouseEnter('Color')"
                (pointerleave)="onMouseLeave('Color', $event)">
                <div class="v-tool-group">
                    <button
                        *ngFor="let color of colorList"
                        class="v-tool-btn"
                        [ngClass]="{
                            active: color === (styleContext$ | async).color,
                        }"
                        (click)="styleTool.applyColor(color)">
                        <span class="vcon vcon-mathtype vcon-color-list">
                            <span class="path1"></span>
                            <span class="path2" [style]="'--color--:' + color"></span>
                        </span>
                    </button>
                </div>
            </div>
        </ng-template>
    </div>
    <span class="v-toolbar-gutter ng-star-inserted"></span>
</div>
