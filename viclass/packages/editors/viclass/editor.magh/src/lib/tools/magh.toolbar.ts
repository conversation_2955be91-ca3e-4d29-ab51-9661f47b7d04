import {
    <PERSON><PERSON><PERSON>,
    CoordinatorEventType,
    DefaultToolBar,
    KeyboardEventData,
    KeyboardEventListener,
    MouseEventListener,
    NativeEventTarget,
    PointerEventListener,
    ToolEventListener,
    ToolState,
    VEventListener,
    ViewportDisableCES,
    ViewportManager,
    ViewportMode,
} from '@viclass/editor.core';
import { MathGraphDocCtrl } from '../docs/magh.doc.ctrl';
import { MathGraphToolEventData } from '../magh.api';
import { MathGraphEditor } from '../magh.editor';
import { MathGraphDocEvent, MathGraphDocFocusedES } from '../magh.models';
import { MathGraphTool } from './magh.tool';
import { MathGraphToolType } from './models';

const LISTEN_DOC_EVENT_TOOLS: MathGraphToolType[] = ['MaghSettingsTool', 'UpdateEquationsTool', 'MaghZoomTool'];

export class MathGraphToolBar extends DefaultToolBar<MathGraphToolType, MathGraphTool<ToolState>> {
    override keyboardHandler?: KeyboardEventListener<NativeEventTarget<any>>;
    override mouseHandler?: MouseEventListener<NativeEventTarget<any>>;
    override pointerHandler?: PointerEventListener<NativeEventTarget<any>>;

    toolListener: ToolEventListener<MathGraphToolBar, any>;
    maghDocEventListener: VEventListener<MathGraphDocEvent>;

    constructor(private editor: MathGraphEditor) {
        super(editor.coordinator);
        this.keyboardHandler = new this._keyboardHandler(this);

        this.toolListener = new this._toolListener(this);
        this.maghDocEventListener = new this.MathGraphDocEventListener(this);
    }

    public override async onViewportModeChanged(vpMode: ViewportMode): Promise<void> {
        this.tools.forEach((tool, type) => {
            switch (vpMode) {
                case 'EditMode': {
                    this.enableTool(type);
                    break;
                }
                case 'InteractiveMode': {
                    switch (type) {
                        case 'MaghZoomTool':
                        case 'MaghPanTool': {
                            this.enableTool(type);
                            break;
                        }
                        default: {
                            this.disableTool(type);
                            break;
                        }
                    }
                    break;
                }
                case 'ViewMode': {
                    switch (type) {
                        case 'MaghZoomTool':
                        case 'MaghPanTool': {
                            this.enableTool(type);
                            break;
                        }
                        default: {
                            this.disableTool(type);
                            break;
                        }
                    }
                    break;
                }
                case 'Disabled': {
                    this.disableTool(type);
                    break;
                }
            }
        });
    }

    override attachViewport(viewport: ViewportManager): void {
        super.attachViewport(viewport);

        this.editor.toolbars.set(viewport.id, this);
        this.editor.selectDelegator.registerDocEventListener(this.maghDocEventListener);
    }

    override detachViewport(viewport: ViewportManager): void {
        super.detachViewport(viewport);

        if (this.maghDocEventListener) {
            this.editor.selectDelegator.unregisterDocEventListener(this.maghDocEventListener);
        }

        this.editor.toolbars.delete(viewport.id);
    }

    private _toolListener = class implements ToolEventListener<MathGraphToolBar, any> {
        constructor(private toolbar: MathGraphToolBar) {}

        onEvent(eventData: MathGraphToolEventData): MathGraphToolEventData | Promise<MathGraphToolEventData> {
            if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(eventData.toolType)) return eventData;
            const tool = eventData.source.getTool(eventData.toolType);
            return tool ? tool.handleToolEvent(eventData) : eventData;
        }
    };

    protected override generateCoordEventListener(): VEventListener<CoordinatorEvent> {
        return new (class implements VEventListener<CoordinatorEvent> {
            constructor(private toolbar: MathGraphToolBar) {}

            onEvent(eventData: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
                const eType = eventData.eventType as CoordinatorEventType;
                const eventState = eventData.state;

                if (!this.toolbar.viewport || eventState.vmId != this.toolbar.viewport.id) return eventData;

                switch (eType) {
                    case 'viewport-disabled': {
                        const evs = eventState as ViewportDisableCES;
                        this.toolbar.editor.selectDelegator.onViewportDisabled(eventData.state.vmId, evs);
                        break;
                    }
                    case 'viewport-removed': {
                        this.toolbar.editor.selectDelegator.onViewportRemoved(eventData.state.vmId);
                        break;
                    }
                }

                return eventData;
            }
        })(this);
    }

    override addTool(toolType: MathGraphToolType, tool: MathGraphTool<ToolState>): void {
        super.addTool(toolType, tool);
        tool.registerToolbar(this);
    }

    private MathGraphDocEventListener = class implements VEventListener<MathGraphDocEvent> {
        constructor(private toolbar: MathGraphToolBar) {}

        onEvent(eventData: MathGraphDocEvent): MathGraphDocEvent | Promise<MathGraphDocEvent> {
            if (this.toolbar.isDisabled()) return eventData;

            if (!this.toolbar.viewport || eventData.state.vm.id != this.toolbar.viewport.id) return eventData;

            switch (eventData.eventType) {
                case 'doc-focused': {
                    const { vm, docCtrl } = eventData.state as MathGraphDocFocusedES;
                    vm.eventManager.captureAllKeyboardEvent(docCtrl.layer.canvas, this.toolbar.keyboardHandler);

                    LISTEN_DOC_EVENT_TOOLS.forEach(toolType => {
                        if (!this.toolbar.isToolDisable(toolType)) {
                            const tool = this.toolbar.getTool(toolType) as any;
                            if (typeof tool.onDocAttached === 'function') {
                                tool.onDocAttached(docCtrl);
                            }
                        }
                    });

                    this.toolbar.blur('CreateMathGraphDocumentTool');
                    break;
                }
                case 'doc-unfocused': {
                    const { vm, docCtrl } = eventData.state as MathGraphDocFocusedES;
                    vm.eventManager.unCaptureAllKeyboardEvent(docCtrl.layer.canvas);

                    LISTEN_DOC_EVENT_TOOLS.forEach(toolType => {
                        if (!this.toolbar.isToolDisable(toolType)) {
                            const tool = this.toolbar.getTool(toolType) as any;
                            if (typeof tool.onDocDetached === 'function') {
                                tool.onDocDetached(docCtrl);
                            }
                        }
                    });

                    docCtrl.unselect();
                    break;
                }
            }

            return eventData;
        }
    };

    private _keyboardHandler = class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(private toolbar: MathGraphToolBar) {}

        onEvent(
            event: KeyboardEventData<NativeEventTarget<any>>
        ): KeyboardEventData<NativeEventTarget<any>> | Promise<KeyboardEventData<NativeEventTarget<any>>> {
            if (this.toolbar.isDisabled() || event.nativeEvent.repeat) return event;
            // currently nothing to do
            return event;
        }
    };
}
