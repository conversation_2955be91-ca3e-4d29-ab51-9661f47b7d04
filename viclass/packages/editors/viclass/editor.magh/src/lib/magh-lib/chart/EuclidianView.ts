import {
    BaseBoardViewportManager,
    BoardViewportManager,
    FEATURE_ZOOM,
    GraphicLayerCtrl,
    Position,
    ScreenPosition,
} from '@viclass/editor.core';
import { MathGraphDocCtrl } from '../../docs/magh.doc.ctrl';
import { MathGraphLayer } from '../../model';
import { DoubleUtil } from '../common';

export class EuclidianView {
    static readonly minZoom = 0.001;
    static readonly maxZoom = 10;

    zoomingToLevel: number;

    /**
     * view center in pixel
     */
    get centerX(): number {
        return this.width / 2 + this.currLookAt.x * this.zoomUnit;
    }
    /**
     * view center in pixel
     */
    get centerY(): number {
        return this.height / 2 + this.currLookAt.y * this.zoomUnit;
    }

    public unit: number;

    constructor(
        public docCtrl: MathGraphDocCtrl,
        public layer: GraphicLayerCtrl
    ) {
        this.unit = this.screenUnit;
    }

    get viewport(): BoardViewportManager {
        return this.docCtrl.viewport as BoardViewportManager;
    }

    // between the layer and the viewport

    get screenUnit(): number {
        return this.docCtrl.state.docRenderProp.screenUnit;
    }

    get currLookAt(): Position {
        return {
            x: this.docCtrl.state.docRenderProp.translation[0],
            y: this.docCtrl.state.docRenderProp.translation[1],
            z: 0,
        };
    }

    get scale(): number {
        if (this.docCtrl.editor.isSupportFeature(FEATURE_ZOOM)) return this.getDocScale() * this.getViewportScale();
        return this.getViewportScale();
    }

    get zoomLevel(): number {
        return this.docCtrl.state.docRenderProp.scale;
    }

    get zoomUnit(): number {
        return this.screenUnit * this.zoomLevel;
    }

    get width(): number {
        if (this.docCtrl.isBoundedView()) return (this.layer.state as MathGraphLayer).boundary.width;
        return this.viewport.viewportWidth();
    }

    get height(): number {
        if (this.docCtrl.isBoundedView()) return (this.layer.state as MathGraphLayer).boundary.height;
        return this.viewport.viewportHeight();
    }

    get left(): number {
        if (this.docCtrl.isBoundedView()) return -this.width / 2;
        return -this.width / 2 + this.viewport.currentLookAt.x;
    }

    get right(): number {
        if (this.docCtrl.isBoundedView()) return this.width / 2;
        return this.width / 2 + this.viewport.currentLookAt.x;
    }

    get bottom(): number {
        if (this.docCtrl.isBoundedView()) return -this.height / 2;
        return -this.height / 2 + this.viewport.currentLookAt.y;
    }

    get top(): number {
        if (this.docCtrl.isBoundedView()) return this.height / 2;
        return this.height / 2 + this.viewport.currentLookAt.y;
    }

    getViewportScale(): number {
        return (this.layer.viewport as BaseBoardViewportManager)?.zoomLevel ?? 1;
    }

    getDocScale(): number {
        return 1 / (this.docCtrl.state.docRenderProp?.scale ?? 1);
    }

    layerToChartLength(l: number): number {
        return l / this.zoomUnit;
    }

    chartToLayerLength(l: number): number {
        return l * this.zoomUnit;
    }

    // chart coord -> layer coord
    chartToLayerCoord(chartCoord: number[]): ScreenPosition {
        return {
            x: this.chartToLayerX(chartCoord[0]),
            y: this.chartToLayerY(chartCoord[1]),
        };
    }

    chartToLayerPos(pos: Position): ScreenPosition {
        return {
            x: this.chartToLayerX(pos.x),
            y: this.chartToLayerY(pos.y),
        };
    }

    // layer coord -> chart coord
    layerToChartPos(layerPos: ScreenPosition): Position {
        return {
            x: this.layerToChartX(layerPos.x),
            y: this.layerToChartY(layerPos.y),
        };
    }

    layerToChartX(x: number): number {
        return x / this.zoomUnit + this.currLookAt.x;
    }

    layerToChartY(y: number): number {
        return y / this.zoomUnit - this.currLookAt.y;
    }

    chartToLayerX(x: number): number {
        return (x - this.currLookAt.x) * this.zoomUnit;
    }

    chartToLayerY(y: number): number {
        return (y + this.currLookAt.y) * this.zoomUnit;
    }

    zoom(level: number, zoomRoot?: Position) {
        let lx = this.currLookAt.x;
        let ly = this.currLookAt.y;
        if (zoomRoot) {
            // calculate current new current look at
            lx = zoomRoot.x - ((zoomRoot.x - lx) * this.zoomUnit) / (level * this.screenUnit);
            ly = ((ly + zoomRoot.y) * this.zoomUnit) / (level * this.screenUnit) - zoomRoot.y;
        }

        this.zoomingToLevel = level;

        this.lookAt(lx, ly);
    }

    lookAt(xInChartCoord: number, yInChartCoord: number) {
        const docRenderProp = this.docCtrl.state.docRenderProp;
        docRenderProp.translation[0] = xInChartCoord;
        docRenderProp.translation[1] = yInChartCoord;

        if (this.zoomingToLevel && this.zoomingToLevel != docRenderProp.scale) {
            docRenderProp.scale = this.zoomingToLevel;
            delete this.zoomingToLevel;
        }
    }

    getChartHeight(): number {
        return this.height / this.zoomUnit;
    }

    getChartWidth(): number {
        return this.width / this.zoomUnit;
    }

    getYmin(): number {
        return this.bottom / this.zoomUnit - this.currLookAt.y;
    }

    getYmax(): number {
        return this.top / this.zoomUnit - this.currLookAt.y;
    }

    getXmin(): number {
        return this.left / this.zoomUnit + this.currLookAt.x;
    }

    getXmax(): number {
        return this.right / this.zoomUnit + this.currLookAt.x;
    }

    containsY(y: number): boolean {
        return y >= this.getYmin() && y <= this.getYmax();
    }

    containsX(x: number): boolean {
        return x >= this.getXmin() && x <= this.getXmax();
    }

    isSegmentOffView(p1: number[], p2: number[]): boolean {
        const tolerance: number = 5 / this.zoomUnit; // around 5px off the screen

        // bottom
        if (
            DoubleUtil.isGreater(this.getYmin(), p1[1], tolerance) &&
            DoubleUtil.isGreater(this.getYmin(), p2[1], tolerance)
        ) {
            return true;
        }

        // top
        if (
            DoubleUtil.isGreater(p1[1], this.getYmax(), tolerance) &&
            DoubleUtil.isGreater(p2[1], this.getYmax(), tolerance)
        ) {
            return true;
        }

        // left
        if (
            DoubleUtil.isGreater(this.getXmin(), p1[0], tolerance) &&
            DoubleUtil.isGreater(this.getXmin(), p2[0], tolerance)
        ) {
            return true;
        }

        // right
        return (
            DoubleUtil.isGreater(p1[0], this.getXmax(), tolerance) &&
            DoubleUtil.isGreater(p2[0], this.getXmax(), tolerance)
        );

        // close to screen
    }
}
