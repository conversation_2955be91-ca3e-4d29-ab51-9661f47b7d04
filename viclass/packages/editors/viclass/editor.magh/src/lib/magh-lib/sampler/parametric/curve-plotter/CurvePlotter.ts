import { EuclidianView } from '../../../chart';
import { CurveEvaluable } from '../../../common';
import { CurveSegmentPlotter } from './CurveSegmentPlotter';
import { Gap } from './Gap';
import { PathPlotter } from './PathPlotter';

/**
 * Class to plot real functions f(x) and 2D/3D parametric curves
 */
export class CurvePlotter {
    // the curve is sampled at least at this many positions to plot it
    private static readonly MIN_SAMPLE_POINTS: number = 80;
    private readonly curveSegmentPlotter: CurveSegmentPlotter;

    /**
     * Draws a parametric curve (x(t), y(t)) for t in [tMin, tMax].
     * @param tMin min value of parameter
     * @param tMax max value of parameter
     * @param curve curve to be drawn
     * @param view Euclidian view to be used
     * @param gp generalpath that can be drawn afterwards
     * @param calcLabelPos whether label position should be calculated and returned
     * @param moveToAllowed whether moveTo() may be used for gp
     */
    public constructor(
        curve: CurveEvaluable,
        tMin: number,
        tMax: number,
        view: EuclidianView,
        gp: PathPlotter,
        calcLabelPos: boolean,
        moveToAllowed: Gap
    ) {
        // ensure MIN_PLOT_POINTS
        const minSamplePoints: number = Math.max(CurvePlotter.MIN_SAMPLE_POINTS, view.width / 6);
        const maxParamStep: number = Math.abs(tMax - tMin) / minSamplePoints;
        // plot Interval [tMin, tMax]
        this.curveSegmentPlotter = new CurveSegmentPlotter(
            curve,
            tMin,
            tMax,
            0,
            maxParamStep,
            view,
            gp,
            calcLabelPos,
            moveToAllowed
        );

        if (moveToAllowed === Gap.CORNER) {
            gp.corner();
        }
    }

    /**
     * Emulates the old behaviour.
     *
     * Draws a parametric curve (x(t), y(t)) for t in [tMin, tMax].
     * @param tMin min value of parameter
     * @param tMax max value of parameter
     * @param curve curve to be drawn
     * @param view Euclidian view to be used
     * @param gp generalpath that can be drawn afterwards
     * @param calcLabelPos whether label position should be calculated and returned
     * @param moveToAllowed whether moveTo() may be used for gp
     * @returns point of the label.
     */
    public static plotCurve = (
        curve: CurveEvaluable,
        tMin: number,
        tMax: number,
        view: EuclidianView,
        gp: PathPlotter,
        calcLabelPos: boolean,
        moveToAllowed: Gap
    ): void => {
        const plotter: CurvePlotter = new CurvePlotter(curve, tMin, tMax, view, gp, calcLabelPos, moveToAllowed);
    };
}
