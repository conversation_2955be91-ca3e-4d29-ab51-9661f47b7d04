import { MaghDocRenderProp } from '../../../model';
import { EuclidianView } from '../../chart/EuclidianView';
import { Sampler } from '../../chart/models';
import { MyPoint } from '../../common/draw/MyPoint';
import { drawArc, drawLabel, drawRect } from '../../common/draw/utils';
import { Domain, PlotEquation } from '../../common/kernel';
import { DoubleUtil } from '../../common/utils/DoubleUtil';
import { Timer } from '../../common/utils/Timer';
import { CornerConfig, QuadTree, SegmentStatus } from './QuadTree';
import { Rect } from './Rect';

export class ImplicitSampler extends QuadTree implements Sampler {
    private static readonly RES_COARSE = 5;
    private static readonly MAX_SPLIT = 40;
    private static fastDrawThreshold = 10;

    private grid!: Rect[][];

    private plotDepth = 0;
    private segmentCheckDepth = 0;

    private sw = 0;
    private sh = 0;

    destroy(): void {
        this.grid.length = 0;
    }

    canHandle(eq: PlotEquation): boolean {
        return eq.isValid && eq.isImplicit;
    }

    setEquation(eq: PlotEquation): void {
        if (!eq.isValid) throw new Error('invalid equation');
        if (!eq.isImplicit) throw new Error('Can not evaluate parametric equation with ImplicitSampler');

        this.geoImplicitCurve.equation = eq;
    }

    sampling(view: EuclidianView, domain?: Domain): MyPoint[] {
        // Calculate intersection between view bounds and domain
        const effectiveDomain = domain || { min: -Infinity, max: Infinity };
        const xMin = Math.max(view.getXmin(), effectiveDomain.min);
        const xMax = Math.min(view.getXmax(), effectiveDomain.max);

        // If no intersection, return empty
        if (xMin >= xMax) {
            return [];
        }

        // Calculate the width based on the restricted domain
        const restrictedWidth = xMax - xMin;

        // start at the left corner of the restricted area
        this.updatePath(
            xMin, // x
            view.getYmin(), // y
            restrictedWidth, // w
            view.getChartHeight(), // h
            view.zoomUnit, // scale X
            view.zoomUnit, // scale Y
            []
        );
        return this.locusPoints;
    }

    public doUpdatePath(): void {
        this.sw = Math.min(ImplicitSampler.MAX_SPLIT, Math.floor((this.w * this.scaleX) / ImplicitSampler.RES_COARSE));
        this.sh = Math.min(ImplicitSampler.MAX_SPLIT, Math.floor((this.h * this.scaleY) / ImplicitSampler.RES_COARSE));

        if (this.sw === 0 || this.sh === 0) {
            return;
        }

        this.grid = new Array(this.sh).fill(null).map(() => new Array(this.sw));

        const frx = this.w / this.sw;
        const fry = this.h / this.sh;

        const vertices: number[] = new Array(this.sw + 1).fill(0);
        const xcoords: number[] = new Array(this.sw + 1).fill(0);
        const ycoords: number[] = new Array(this.sh + 1).fill(0);

        for (let i = 0; i <= this.sw; i++) {
            xcoords[i] = this.x + i * frx;
        }

        for (let i = 0; i <= this.sh; i++) {
            ycoords[i] = this.y + i * fry;
        }

        for (let i = 0; i <= this.sw; i++) {
            vertices[i] = this.evaluateImplicitCurve(xcoords[i], ycoords[0]);
        }

        // Initialize grid configuration at the search depth
        const timer = new Timer();
        let dx, dy, fx, fy: number;
        timer.reset();

        for (let i = 1; i <= this.sh; i++) {
            let prev = this.evaluateImplicitCurve(xcoords[0], ycoords[i]);
            fy = ycoords[i] - 0.5 * fry;

            for (let j = 1; j <= this.sw; j++) {
                const cur = this.evaluateImplicitCurve(xcoords[j], ycoords[i]);

                const rect: Rect = new Rect(j - 1, i - 1, frx, fry, false);
                rect.coords.val[0] = xcoords[j - 1];
                rect.coords.val[1] = ycoords[i - 1];
                rect.evals[0] = vertices[j - 1];
                rect.evals[1] = vertices[j];
                rect.evals[2] = cur;
                rect.evals[3] = prev;
                rect.status = this.edgeConfig(rect);
                rect.shares = 0xff;
                fx = xcoords[j] - 0.5 * frx;
                dx = this.derivativeX(fx, fy);
                dy = this.derivativeY(fx, fy);
                dx = Math.abs(dx) + Math.abs(dy);

                if (DoubleUtil.isZero(dx, 0.001)) {
                    rect.singular = true;
                }

                this.grid[i - 1][j - 1] = rect;
                vertices[j - 1] = prev;
                prev = cur;
            }

            vertices[this.sw] = prev;
        }

        timer.record();

        if (timer.elapse <= ImplicitSampler.fastDrawThreshold) {
            this.plotDepth = 3;
            this.segmentCheckDepth = 2;
            this.LIST_THRESHOLD = 48;
        } else {
            this.plotDepth = 2;
            this.segmentCheckDepth = 1;
            this.LIST_THRESHOLD = 24;
        }

        for (let i = 0; i < this.sh; i++) {
            for (let j = 0; j < this.sw; j++) {
                if (this.grid[i][j].status !== SegmentStatus.EMPTY) {
                    this.plot(this.grid[i][j], 0);
                }
            }
        }

        timer.record();

        if (timer.elapse >= 500) {
            return;
        } else if (timer.elapse >= 300) {
            this.plotDepth -= 1;
            this.segmentCheckDepth -= 1;
        }
    }

    public createTree = (r: Rect, depth: number): void => {
        const n: Rect[] = r.split(this.geoImplicitCurve);
        this.plot(n[0], depth);
        this.plot(n[1], depth);
        this.plot(n[2], depth);
        this.plot(n[3], depth);
    };

    public plot = (r: Rect, depth: number): void => {
        if (depth < this.segmentCheckDepth) {
            this.createTree(r, depth + 1);
            return;
        }
        const e: number = this.edgeConfig(r);
        if (this.grid[r.y][r.x].singular || e !== SegmentStatus.EMPTY) {
            if (depth >= this.plotDepth) {
                if (this.addSegment(r) === CornerConfig.T0101) {
                    this.createTree(r, depth + 1);
                    return;
                }
                if (r.x !== 0 && (e & r.shares & 0x1) !== 0) {
                    this.nonempty(r.y, r.x - 1);
                }
                if (r.x + 1 !== this.sw && (e & r.shares & 0x4) !== 0) {
                    this.nonempty(r.y, r.x + 1);
                }
                if (r.y !== 0 && (e & r.shares & 0x8) !== 0) {
                    this.nonempty(r.y - 1, r.x);
                }
                if (r.y + 1 !== this.sh && (e & r.shares & 0x2) !== 0) {
                    this.nonempty(r.y + 1, r.x);
                }
            } else {
                this.createTree(r, depth + 1);
            }
        }
    };

    private nonempty = (ry: number, rx: number): void => {
        if (this.grid[ry][rx].status === SegmentStatus.EMPTY) {
            this.grid[ry][rx].status = 1;
        }
    };

    // 	public polishPointOnPath = (pt: GeoPointND): void => {
    // 		pt.updateCoords();
    // 		let x1: number = this.onScreen(pt.getInhomX(), this.x, this.x + this.w);
    // 		let y1: number = this.onScreen(pt.getInhomY(), this.y, this.y + this.h);
    // 		let d1: number = this.evaluateImplicitCurve(x1, y1);
    // 		if (DoubleUtil.isZero(d1)) {
    // 			pt.setCoords(new Coords(x1, y1, 1.0), false);
    // 			return;
    // 		}
    //
    // 		// determine the direction of the gradient vector
    // 		const derivativeX: number = this.getDerivativeX().evaluate(x1, y1);
    // 		const derivativeY: number = this.getDerivativeY().evaluate(x1, y1);
    // 		const derivativeLength: number = Math.hypot(derivativeX, derivativeY);
    //
    // 		// take one big step in the direction of the gradient vector
    // 		const mv: number = Math.max(this.w, this.h) / QuadTreeImpl.MAX_SPLIT;
    // 		let x2: number = x1 - mv * (derivativeX / derivativeLength) * Math.sign(d1);
    // 		let y2: number = y1 - mv * (derivativeY / derivativeLength) * Math.sign(d1);
    // 		let d2: number = this.evaluateImplicitCurve(x2, y2);
    //
    // 		// if we stepped over the curve...
    // 		if (d2 * d1 <= 0.0) {
    // 			let mx: number = x1;
    // 			let my: number = y1;
    //
    // 			// binary search the closest point to the curve with a maximum depth of 64
    // 			for (
    // 				let count: number = 0;
    // 				count < 64 && !DoubleUtil.isZero(d2);
    // 				count++
    // 			) {
    // 				mx = 0.5 * (x1 + x2);
    // 				my = 0.5 * (y1 + y2);
    // 				const md: number = this.evaluateImplicitCurve(mx, my);
    // 				if (DoubleUtil.isZero(md)) {
    // 					pt.setCoords(new Coords(mx, my, 1.0), false);
    // 					return;
    // 				}
    // 				if (d1 * md <= 0.0) {
    // 					d2 = md;
    // 					x2 = mx;
    // 					y2 = my;
    // 				} else {
    // 					d1 = md;
    // 					x1 = mx;
    // 					y1 = my;
    // 				}
    // 			}
    // 			// we didn't hit exact 0, let's use the closest we have
    // 			pt.setCoords(new Coords(mx, my, 1.0), false);
    // 		}
    // 	};

    // private onScreen = (v: number, mn: number, mx: number): number => {
    // 	if (Number.isNaN(v) || !Number.isFinite(v) || v < mn || v > mx) {
    // 		return (mn + mx) * 0.5;
    // 	}
    // 	return v;
    // };

    public derivativeX(x: number, y: number): number {
        const dx = 1e-6; // Change in x

        const fPlusDx = this.evaluateImplicitCurve(x + dx, y);
        const fMinusDx = this.evaluateImplicitCurve(x - dx, y);

        return (fPlusDx - fMinusDx) / (2 * dx);
    }

    public derivativeY(x: number, y: number): number {
        const dy = 1e-6; // Change in y

        const fPlusDy = this.evaluateImplicitCurve(x, y + dy);
        const fMinusDy = this.evaluateImplicitCurve(x, y - dy);

        return (fPlusDy - fMinusDy) / (2 * dy);
    }

    public derivativeXY(x: number, y: number): number {
        const dx = 1e-6; // Small change in x
        const dy = 1e-6; // Small change in y

        const fPlusDxPlusDy = this.evaluateImplicitCurve(x + dx, y + dy);
        const fPlusDxDy = this.evaluateImplicitCurve(x + dx, y);
        const fPlusDyDx = this.evaluateImplicitCurve(x, y + dy);
        const fMinusDxMinusDy = this.evaluateImplicitCurve(x - dx, y - dy);
        const fMinusDxDy = this.evaluateImplicitCurve(x - dx, y);
        const fMinusDyDx = this.evaluateImplicitCurve(x, y - dy);

        // Calculate partial derivatives
        const dfdx =
            (fPlusDxPlusDy - fPlusDxDy - fPlusDyDx + fMinusDxMinusDy + fMinusDxDy + fMinusDyDx) / (4 * dx * dy);

        const dfdy =
            (fPlusDxPlusDy - fPlusDxDy - fPlusDyDx + fMinusDxMinusDy + fMinusDxDy + fMinusDyDx) / (4 * dx * dy);

        // Calculate mixed partial derivative
        return (dfdx - dfdy) / (2 * dx);
    }

    drawDebug(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void {
        const frx = this.w / this.sw;
        const fry = this.h / this.sh;
        const rWidth = frx * view.zoomUnit;
        const rHeight = fry * view.zoomUnit;
        const lxOffset = rWidth * 0.3;
        const lyOffset = rHeight * 0.2;
        const lFontSize = 8 * view.getViewportScale();
        const nanRad = 3 * view.getViewportScale();
        for (const row of this.grid) {
            for (const rect of row) {
                const x = view.chartToLayerX(rect.coords.getX());
                const y = view.chartToLayerY(rect.coords.getY());
                drawRect(ctx, x, y, rWidth, rHeight, {
                    strokeColor: 'rgb(156, 117, 117)',
                    lineWeight: 0.1,
                    fillColor: rect.status > 0 ? 'rgba(224, 85, 79, 0.4)' : undefined,
                });

                drawLabel(
                    ctx,
                    `${rect.y},${rect.x}`,
                    {
                        x: x + lxOffset,
                        y: y + lyOffset,
                    },
                    { fontSize: lFontSize, strokeText: false }
                );

                for (let i = 0; i < 4; i++) {
                    const evalVal = rect.evals[i];
                    if (isNaN(evalVal)) {
                        const eX = view.chartToLayerX(i === 0 || i === 3 ? rect.x1() : rect.x2());
                        const eY = view.chartToLayerY(i === 0 || i === 1 ? rect.y1() : rect.y2());
                        drawArc(ctx, { x: eX, y: eY }, nanRad, 0, 2 * Math.PI, { color: 'red' });
                    }
                }
            }
        }
    }
}
