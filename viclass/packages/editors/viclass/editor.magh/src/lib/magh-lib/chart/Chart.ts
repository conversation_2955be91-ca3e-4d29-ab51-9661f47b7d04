import { GraphicLayerCtrl } from '@viclass/editor.core';
import { Subject, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { MathGraphDocCtrl } from '../../docs/magh.doc.ctrl';
import { Engine, EqType, Equation, PlotEquation, PointEquation, Scope, VarEquation } from '../common';
import { EuclidianView } from './EuclidianView';
import { Grid } from './Grid';
import { Destroyable } from './models';

/**
 * Represent the whole chart with the grid and multiple equations
 */
export class Chart implements Destroyable {
    static DEBUG_MODE = false;

    private started = false;
    private destroyed = false;

    private canvas: HTMLCanvasElement;
    private ctx: CanvasRenderingContext2D;
    private grid: Grid = new Grid();

    readonly equations: Equation[] = [];

    readonly equationsUpdated$ = new Subject<void>();

    /**
     * Compute engine instance for the chart.
     * Can not share between charts because each chart can define their own declarations/functions.
     */
    private readonly engine = new Engine();

    colorPalette = ['#FF002E', '#121414', '#00AEEF', '#DB00FF', '#31E37C', '#FFD600', '#FF7A00'];

    readonly view: EuclidianView;

    private reloading = false;
    private debounceRedraw$ = new Subject<void>();
    private debounceRedrawSubscription: Subscription = this.debounceRedraw$
        .pipe(debounceTime(200))
        .subscribe(() => this.redraw());

    constructor(
        private docCtrl: MathGraphDocCtrl,
        private layer: GraphicLayerCtrl
    ) {
        // Scale canvas for high-DPI displays
        this.canvas = this.layer.canvas;
        this.ctx = this.layer.ctx as CanvasRenderingContext2D;

        this.view = new EuclidianView(this.docCtrl, this.layer);
    }

    /**
     * Start the chart after initialization
     */
    start(): void {
        this.reloadChart();

        if (!this.started) {
            this.started = true;
            this.layer.attachRenderer(() => {
                this.redraw();
                this.reloadChart();
            });
        }
    }

    /**
     * Destroy and clear all equations
     */
    clearEquations(): void {
        this.equations.forEach(eq => eq.destroy());
        this.equations.length = 0;
    }

    /**
     * Destroy and clean up the chart
     */
    destroy(): void {
        this.destroyed = true;
        this.clearEquations();
        this.equationsUpdated$.complete();

        this.debounceRedrawSubscription?.unsubscribe();
        this.debounceRedraw$.complete();
    }

    /**
     * Create a new equation with the style from the default of document.
     * Notice that this equation is not added to the chart here.
     * @returns a new equation
     */
    createEquation(equationType?: EqType): Equation {
        let eq: Equation;
        if (equationType === EqType.Point) {
            eq = new PointEquation(this.engine, this.getGlobalScope.bind(this));
        } else if (equationType === EqType.ScopeVar) {
            eq = new VarEquation(this.engine, this.getGlobalScope.bind(this));
        } else {
            eq = new PlotEquation(
                this.engine,
                this.getGlobalScope.bind(this),
                this.docCtrl.overlayLayerCtrl.nativeEl as HTMLElement,
                () => this.debounceRedraw$.next()
            );
        }

        const docRenderProp = this.docCtrl.state.docRenderProp;
        eq.styles.fillOpacity = docRenderProp.opacity ?? 1;
        eq.styles.lineStyle = docRenderProp.lineStyle;
        eq.styles.lineWidth = docRenderProp.lineWidth;

        return eq;
    }

    /**
     * Add an equation to the chart
     * @param eq Equation to be added
     */
    addEquation(eq: Equation): void {
        if (eq.equationType === EqType.Plot) {
            const color = this.nextColor();
            eq.styles.color = color;
        }

        this.equations.push(eq);
        this.equationsUpdated$.next();
    }

    /**
     * Remove an equation by index in the equations array
     */
    removeEquationByIndex(i: number): Equation | null {
        const [removed] = this.equations.splice(i, 1);

        if (removed) {
            this.equationsUpdated$.next();
            return removed;
        }

        return null;
    }

    /**
     * Remove the equation instance from the chart
     */
    removeEquation(eq: Equation): Equation | null {
        const i = this.equations.indexOf(eq);
        if (i !== -1) {
            return this.removeEquationByIndex(i);
        }
        return null;
    }

    /**
     * Set the expression of an equation by index
     */
    setExpression(i: number, expr: string, silent = false): void {
        const eq = this.equations[i];
        if (!eq) return;
        try {
            eq.setExpression(expr);
            if (!silent) {
                this.equationsUpdated$.next();
            }
        } catch (e) {
            console.warn('set expression failed', e);
        } finally {
            this.reloadChart();
        }
    }

    /**
     * Reload the chart on the next animation frame
     */
    reloadChart = () => {
        if (this.destroyed || this.reloading) return;

        this.reloading = true;
        requestAnimationFrame(this.updateAndRedraw);
    };

    /**
     * Update all equations and redraw the chart immediately
     */
    private updateAndRedraw = () => {
        try {
            // console.time('update plots');
            this.equations.forEach(eq => eq.updatePath(this.view));
            // console.timeEnd('update plots');

            this.redraw();
        } catch (e) {
            console.error(e);
        } finally {
            this.reloading = false;
        }
    };

    /**
     * Redraw the chart, this method is only drawing, not update/re-evaluate the equations
     */
    private redraw() {
        const drp = this.docCtrl.state.docRenderProp;
        this.clearCanvas();
        // Draw equations background
        this.equations.forEach(eq => eq.drawBackground?.(this.ctx, this.view, drp));
        // Draw grid
        this.grid.draw(this.ctx, this.view, drp);
        // Draw equations main content
        this.equations.forEach(eq => eq.draw(this.ctx, this.view, drp));
        // Draw equations labels
        this.equations.forEach(eq => eq.drawLabels(this.ctx, this.view, drp));
    }

    /**
     * Clear the canvas
     */
    private clearCanvas() {
        this.ctx.save();
        this.ctx.setTransform(1, 0, 0, 1, 0, 0);

        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.restore();
    }

    /**
     * The global scope equation is the one to provide value for other equations.
     * Example eq1: `a = 1`; eq2: `a + x = 0` then eq2 can infer `a = 1` from `eq1`.
     * Get the global variable scope from other equations.
     *
     * @param ownEq The current equatiion who request the scope.
     *              So a global scope can refer to another global scope.
     *              Example eq3: `b = a + 1` can infer `a = 1` from the eq1 from above
     * @returns the global variable scope from other equations
     */
    private getGlobalScope(ownEq: Equation): Scope {
        const scope: Scope = {};
        for (const equation of this.equations) {
            if (equation === ownEq) continue;

            if (equation.equationType === EqType.ScopeVar && equation instanceof VarEquation) {
                const eqScope = equation.globalScope;
                const key = eqScope.key;
                const value = eqScope.value;
                if (scope[key] !== undefined) continue;
                scope[key] = value;
            }
        }
        return scope;
    }

    /**
     * Get the next color from the color palette so the next equation will have a different color
     */
    private nextColor(): string {
        if (!this.equations.length) return this.colorPalette[0];
        const lastColor = this.equations[this.equations.length - 1].styles.color;
        const i = this.colorPalette.indexOf(lastColor);
        if (i === -1) return this.colorPalette[0];

        return this.colorPalette[(i + 1) % this.colorPalette.length];
    }
}
