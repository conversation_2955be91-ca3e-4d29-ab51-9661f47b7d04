import { EuclidianView } from '../../../chart';
import { CurveEvaluable, Kernel } from '../../../common';
import { CurveSegmentPlotter } from './CurveSegmentPlotter';

/**
 * Class to query basic information for a segment about to draw
 * like it is on screen, the distance is not "big" enough
 * or has an angle that is not suitable to draw.
 */
export class CurveSegmentInfo {
    public static readonly MAX_PIXEL_DISTANCE: number = 10; // pixels
    private static readonly MAX_ANGLE: number = 10; // degrees
    private static readonly MAX_ANGLE_OFF_SCREEN: number = 45; // degrees
    public static readonly MAX_BEND: number = Math.tan(CurveSegmentInfo.MAX_ANGLE * Kernel.PI_180);
    private static readonly MAX_BEND_OFF_SCREEN: number = Math.tan(
        CurveSegmentInfo.MAX_ANGLE_OFF_SCREEN * Kernel.PI_180
    );

    private distanceOK: boolean = false;
    private angleOK: boolean = false;
    private offScreen: boolean = false;
    private reachedminStep: boolean = false;

    constructor(private view: EuclidianView) {}

    /**
     *
     * @returns if segment is off the screen
     */
    public isOffScreen = (): boolean => {
        return this.offScreen;
    };

    /**
     * updates info
     *
     * @param evalLeft left value.
     * @param evalRight right value.
     * @param diff left-right difference in pixels.
     * @param prevDiff the
     */
    public update = (
        evalLeft: number[],
        evalRight: number[],
        diff: number[],
        prevDiff: number[],
        curve: CurveEvaluable
    ): void => {
        this.offScreen = this.view.isSegmentOffView(evalLeft, evalRight);
        this.reachedminStep = Math.abs(diff[0]) < curve.getMinDistX();
        this.distanceOK = CurveSegmentInfo.isDistanceOK(diff);
        this.angleOK = CurveSegmentInfo.isAngleOK(
            prevDiff,
            diff,
            this.offScreen ? CurveSegmentInfo.MAX_BEND_OFF_SCREEN : CurveSegmentInfo.MAX_BEND
        );
    };

    /**
     *
     * @return true if distance or angle is not valid for drawing.
     */
    public isDistanceOrAngleInvalid = (): boolean => {
        return !this.angleOK || !this.distanceOK;
    };

    /**
     * Returns whether the pixel distance from the last point is smaller than
     * MAX_PIXEL_DISTANCE in all directions.
     */
    private static isDistanceOK = (diff: number[]): boolean => {
        for (const d of diff) {
            if (Math.abs(d) > CurveSegmentInfo.MAX_PIXEL_DISTANCE) {
                return false;
            }
        }
        return true;
    };

    /**
     * Returns whether the angle between the vectors (vx, vy) and (wx, wy) is
     * smaller than MAX_BEND, where MAX_BEND = tan(MAX_ANGLE).
     */
    private static isAngleOK = (v: number[], w: number[], bend: number): boolean => {
        // |v| * |w| * sin(alpha) = |det(v, w)|
        // cos(alpha) = v . w / (|v| * |w|)
        // tan(alpha) = sin(alpha) / cos(alpha)
        // tan(alpha) = |det(v, w)| / v . w

        // small angle: tan(alpha) < MAX_BEND
        // |det(v, w)| / v . w < MAX_BEND
        // |det(v, w)| < MAX_BEND * (v . w)

        let innerProduct: number = 0;
        for (let i: number = 0; i < v.length; i++) {
            innerProduct += v[i] * w[i];
        }
        if (CurveSegmentPlotter.isUndefined(innerProduct)) {
            return true;
        } else {
            if (innerProduct <= 0) {
                // angle >= 90 degrees
                return false;
            } else {
                // angle < 90 degrees
                // small angle: |det(v, w)| < MAX_BEND * (v . w)
                let det: number;
                if (v.length < 3) {
                    det = Math.abs(v[0] * w[1] - v[1] * w[0]);
                } else {
                    const d1: number = v[0] * w[1] - v[1] * w[0];
                    const d2: number = v[1] * w[2] - v[2] * w[1];
                    const d3: number = v[2] * w[0] - v[0] * w[2];
                    det = Math.sqrt(d1 * d1 + d2 * d2 + d3 * d3);
                }
                return det < bend * innerProduct;
            }
        }
    };

    public hasNotReachedMinStep = (): boolean => {
        return !this.reachedminStep;
    };
}
