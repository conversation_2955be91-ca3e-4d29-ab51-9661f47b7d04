import { reliableSaveCmdMeta } from '@viclass/editor.core';
import { DocRenderPropProto, MathGraphCmdTypeProto } from '@viclass/proto/editor.magh';
import { MathGraphDocCtrl } from '../docs/magh.doc.ctrl';
import { MaghDocRenderProp, SerializedEquation } from '../magh.api';
import { DeleteEquationCmd, UpdateContentCmd, UpdateDocStateCmd } from './magh.cmd';

export function syncContentUpdateCommand(
    docCtrl: MathGraphDocCtrl,
    index: number,
    equation: SerializedEquation,
    isInsert = false
) {
    const meta = reliableSaveCmdMeta(
        docCtrl.viewport,
        docCtrl.state,
        docCtrl.state.id,
        docCtrl.state.id,
        MathGraphCmdTypeProto.UPDATE_CONTENT
    );
    const cmd = new UpdateContentCmd(meta);
    cmd.setVersion(docCtrl.state.version);
    cmd.setGlobalId(docCtrl.state.globalId);

    cmd.setPlotIndex(index);
    cmd.setEquation(equation);
    if (isInsert) {
        cmd.setIsInsert(true);
    }

    docCtrl.editor.sendCommand(cmd);
}

export function convertDocRenderPropToProto(docRenderProp: MaghDocRenderProp): DocRenderPropProto {
    const proto = new DocRenderPropProto();

    if (docRenderProp.screenUnit !== undefined) proto.setScreenUnit(docRenderProp.screenUnit);
    if (docRenderProp.scale !== undefined) proto.setScale(docRenderProp.scale);
    if (docRenderProp.translation !== undefined) proto.setTranslationList(docRenderProp.translation);
    if (docRenderProp.rotation !== undefined) proto.setRotationList(docRenderProp.rotation);
    if (docRenderProp.valid !== undefined) proto.setValid(docRenderProp.valid);
    if (docRenderProp.axis !== undefined) proto.setAxis(docRenderProp.axis);
    if (docRenderProp.grid !== undefined) proto.setGrid(docRenderProp.grid);
    if (docRenderProp.detailGrid !== undefined) proto.setDetailGrid(docRenderProp.detailGrid);
    if (docRenderProp.showAxisArrows !== undefined) proto.setShowAxisArrows(docRenderProp.showAxisArrows);
    if (docRenderProp.showAxisLabels !== undefined) proto.setShowAxisLabels(docRenderProp.showAxisLabels);
    if (docRenderProp.usePiGrid !== undefined) proto.setUsePiGrid(docRenderProp.usePiGrid);
    if (docRenderProp.lineStyle !== undefined) proto.setLineStyle(docRenderProp.lineStyle);
    if (docRenderProp.lineWidth !== undefined) proto.setLineWidth(docRenderProp.lineWidth);
    if (docRenderProp.opacity !== undefined) proto.setOpacity(docRenderProp.opacity);

    return proto;
}

export function convertProtoToDocRenderProp(proto: DocRenderPropProto): MaghDocRenderProp {
    const docRenderProp = <MaghDocRenderProp>{};

    if (proto.hasScreenUnit()) docRenderProp.screenUnit = proto.getScreenUnit();
    if (proto.hasScale()) docRenderProp.scale = proto.getScale();
    if (proto.getTranslationList().length) docRenderProp.translation = proto.getTranslationList();
    else docRenderProp.translation = [0, 0, 0];
    if (proto.getRotationList().length) docRenderProp.rotation = proto.getRotationList();
    else docRenderProp.rotation = [0, 0, 0];
    if (proto.hasValid()) docRenderProp.valid = proto.getValid();
    if (proto.hasShowAxisArrows()) docRenderProp.showAxisArrows = proto.getShowAxisArrows();
    if (proto.hasShowAxisLabels()) docRenderProp.showAxisLabels = proto.getShowAxisLabels();
    if (proto.hasUsePiGrid()) docRenderProp.usePiGrid = proto.getUsePiGrid();
    if (proto.hasAxis()) docRenderProp.axis = proto.getAxis();
    if (proto.hasGrid()) docRenderProp.grid = proto.getGrid();
    if (proto.hasDetailGrid()) docRenderProp.detailGrid = proto.getDetailGrid();
    if (proto.hasLineStyle()) docRenderProp.lineStyle = proto.getLineStyle();
    if (proto.hasLineWidth()) docRenderProp.lineWidth = proto.getLineWidth();
    if (proto.hasOpacity()) docRenderProp.opacity = proto.getOpacity();

    return docRenderProp;
}

export function syncUpdateDocStateCommand(docCtrl: MathGraphDocCtrl) {
    const doc = docCtrl.state;
    const meta = reliableSaveCmdMeta(docCtrl.viewport, doc, doc.id, doc.id, MathGraphCmdTypeProto.UPDATE_DOC_STATE);
    const cmd = new UpdateDocStateCmd(meta);
    cmd.setState(doc.globalId, convertDocRenderPropToProto(doc.docRenderProp));

    docCtrl.editor.cmdChannel.receive(cmd);
}

export function syncDeleteEquationCommand(docCtrl: MathGraphDocCtrl, idx: number) {
    const doc = docCtrl.state;
    const meta = reliableSaveCmdMeta(docCtrl.viewport, doc, doc.id, doc.id, MathGraphCmdTypeProto.DELETE_EQUATION);
    const cmd = new DeleteEquationCmd(meta);
    cmd.state.setGlobalId(doc.globalId).setVersion(doc.version).setPlotIndex(idx);

    docCtrl.editor.cmdChannel.receive(cmd);
}
