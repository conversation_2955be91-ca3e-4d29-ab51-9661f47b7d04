import { SelectionFeature, ToolState, ViewportId } from '@viclass/editor.core';
import { WordEditor } from '../word.editor';
import { WordToolType } from './models';
import { WordTool } from './word.tool';
import { WordDocCtrl } from '../docs/word.doc.ctrl';

export type ContentStateType = 'Disable' | 'Enable';

/**
 * This tool represents the ability to edit the currently focused word document. If this tool is disabled, the focused word document is not editable.
 * If this tool is enabled, the focused word document is editable if it is the only focused document on the viewport.
 */
export class ContentTool extends WordTool<ToolState, any> {
    readonly toolType: WordToolType = 'ContentEditorTool';
    toolState: ToolState;

    private vpId?: string;

    constructor(editor: WordEditor) {
        super(editor);
    }

    checkFocusedDocEditable(viewportId: ViewportId, doc: WordDocCtrl, selectFeature: SelectionFeature): void {
        const selectTool = selectFeature.getSelectToolByVp(viewportId);

        // Can be edited when the number of selects is 1
        if (
            selectTool &&
            selectTool.toolState.numDocSelected == 1 &&
            !this.editor.toolbars.get(viewportId).isToolDisable('ContentEditorTool')
        )
            doc.enableEditMode();
        //disable edit when selecting multiple objects
        else this.onDisable();
    }

    override onDisable() {
        if (!this.vpId) return;

        const focusedDocs = this.editor.selectDelegator.getFocusedDocs(this.vpId);
        if (focusedDocs) this.editor.selectDelegator.getFocusedDocs(this.vpId).forEach(e => e.disableEditMode());
        this.vpId = undefined;
    }

    override onEnable() {
        this.vpId = this.toolbar.viewport.id;
        const selectTool = this.editor.selectionFeature.getSelectToolByVp(this.vpId);
        const focusedDocs = this.editor.selectDelegator.getFocusedDocs(this.vpId);
        if (selectTool && selectTool.toolState.numDocSelected === 1 && focusedDocs?.length === 1)
            this.editor.selectDelegator.getFocusedDocs(this.vpId)[0].enableEditMode();
    }
}
