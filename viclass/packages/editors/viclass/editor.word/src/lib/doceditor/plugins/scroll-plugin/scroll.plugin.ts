import { BaseBoardViewportManager, reliableCmdMeta } from '@viclass/editor.core';
import { WordCmdTypeProto } from '@viclass/proto/editor.word';
import { SyncWordDocScrollCmd } from '../../../cmds/word.cmd';
import { WordPlugin } from '../word.plugin';

export class ScrollPlugin extends WordPlugin {
    private anchorScroll = 0;

    override init(): void {
        const root = this.wordLib.rootElement;
        root.addEventListener('scroll', this.handleDocScroll);

        this.addUnsubscribe(() => root.removeEventListener('scroll', this.handleDocScroll));
    }

    private handleDocScroll = async () => {
        const scroll = this.wordLib?.rootElement?.scrollTop;
        if (!isFinite(scroll)) return;

        this.updateWordCursorContainer(scroll);

        if (!this.lexical.isEditable()) return;

        const meta = reliableCmdMeta(
            this.docCtrl.viewport,
            this.docCtrl.state.id,
            this.docCtrl.state.id,
            WordCmdTypeProto.SYNC_WORD_DOC_SCROLL
        );
        const cmd = new SyncWordDocScrollCmd(meta);
        cmd.setScrollTop(scroll);
        await this.docCtrl.editor.sendCommand(cmd);
    };

    private updateWordCursorContainer(scroll: number) {
        const cursorsContainer = this.wordLib.binding?.cursorsContainer;
        if (!cursorsContainer || !isFinite(scroll)) return;

        if (cursorsContainer.getAttribute('scrollAnchor')) {
            cursorsContainer.removeAttribute('scrollAnchor');
            this.anchorScroll = scroll;
        }

        const vmZoom = this.docCtrl.viewport instanceof BaseBoardViewportManager ? this.docCtrl.viewport.zoomLevel : 1;
        cursorsContainer.style.top = (this.anchorScroll - scroll) / vmZoom + 'px';
    }
}
