/**
 * Configuration of the word editor extends the basic configuration and provides additionally lookups so that
 * the word editor can act like a coordinator and load its own instances of editors.
 *
 * Word editor doesn't create the UI of its internal editor, instead, the application which host the word editor
 * needs to create the editor UI and supply it to the word editor through its API.
 */

import { EditorConfig, EditorCoordinatorConfig, EditorLookup } from '@viclass/editor.core';

export type EditorViewportType = 'board' | 'inline';
export interface EditorViewportConfig {
    vpType: EditorViewportType;
    // if not set, default to 100%
    defaultWidth?: string;
    // if not set, default to 250px
    defaultHeight?: string;
}

export interface WordEditorCoordinatorConfig extends EditorCoordinatorConfig {
    syncRouting: boolean;
    edLookups: EditorLookup[];
    // a map from editor type to the viewport type to be used for that editor
    viewport: {
        [key: string]: EditorViewportConfig;
    };
    // element class which is attached to the viewport element
    viewportElClass: string;
}

export interface WordEditorConfig extends EditorConfig {
    wcoordConf: WordEditorCoordinatorConfig;
    commonTheme: string;
    wordTheme: string;
}
