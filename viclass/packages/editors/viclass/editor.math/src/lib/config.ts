/**
 * Configuration of the math editor extends the basic configuration and provides additionally lookups so that
 * the math editor can act like a coordinator and load its own instances of editors.
 *
 * Math editor doesn't create the UI of its internal editor, instead, the application which host the math editor
 * needs to create the editor UI and supply it to the math editor through its API.
 */

import { EditorConfig } from '@viclass/editor.core';

export type EditorViewportType = 'board' | 'inline';
export interface EditorViewportConfig {
    vpType: EditorViewportType;
    // if not set, default to 100%
    defaultWidth?: string;
    // if not set, default to 250px
    defaultHeight?: string;
}

export interface MathEditorConfig extends EditorConfig {
    fontsUri: string;
    soundsUri: string;
}
