import { DocLocalId, ViewportId } from '@viclass/editor.core';

export function mathDocReg(viewportId: ViewportId) {
    return `mathEditor/mathDoc/${viewportId}`;
}

export function mathLayerReg(viewportId: ViewportId, docId: DocLocalId) {
    return `mathEditor/mathLayer/${viewportId}/${docId}`;
}

export function mathObjectReg(viewportId: ViewportId, docId: DocLocalId) {
    return `mathEditor/mathObject/${viewportId}/${docId}`;
}

export function loadCSS(url: string, rootDoc?: Document) {
    rootDoc = rootDoc || document;

    // Check if the CSS has already been loaded
    const existingStylesheet = Array.from(rootDoc.styleSheets).find(sheet => sheet.href === url);
    if (!existingStylesheet) {
        // Create a <link> tag
        const link = rootDoc.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = url;

        // Append the <link> tag to the document head
        rootDoc.head.appendChild(link);

        // Listen for the load event
        link.addEventListener('load', () => {
            console.log('CSS loaded:', url);
        });
    } else {
        console.log('CSS already loaded:', url);
    }
}

export function removeClassesWithPrefix(elem: HTMLElement | SVGElement, prefix: string): void {
    Array.from(elem.classList).forEach(cls => {
        if (cls.startsWith(prefix)) {
            elem.classList.remove(cls);
        }
    });
}
