import { LatexGroup } from './latex.model';

export const RELATION_BINARY_GROUP: LatexGroup = {
    type: 'group',
    label: 'Large Operators',
    icon: 'group_binary-nhi-phan',
    children: [
        {
            type: 'sub-group',
            children: [
                {
                    type: 'item',
                    latex: '\\mp',
                    icon: 'minus-plus',
                    label: {
                        en: 'minus - plus',
                        vi: 'trừ - cộng',
                    },
                },
                {
                    type: 'item',
                    latex: '\\pm',
                    icon: 'plus-minus',
                    label: {
                        en: 'plus - minus',
                        vi: 'cộng - trừ',
                    },
                },
                {
                    type: 'item',
                    latex: '\\div',
                    icon: 'division-sign',
                    label: {
                        en: 'division sign / obelus',
                        vi: 'Dấu Hiệu Phân Chia / Tháp',
                    },
                },
                {
                    type: 'item',
                    latex: '\\ast',
                    icon: 'asterisk',
                    label: {
                        en: 'asterisk',
                        vi: 'Hoa Thị',
                    },
                },
                {
                    type: 'item',
                    latex: '\\setminus',
                    icon: 'set-minus',
                    label: {
                        en: 'Set Minus',
                    },
                },
                {
                    type: 'item',
                    latex: '\\dotplus',
                    icon: 'dot-plus',
                    label: {
                        en: 'Dot Plus',
                    },
                },
                {
                    type: 'item',
                    latex: '\\prod',
                    icon: 'product-sign',
                    label: {
                        en: 'Product Sign',
                    },
                },
                {
                    type: 'item',
                    latex: '\\coprod',
                    icon: 'coproduct-sign',
                    label: {
                        en: 'Coproduct Sign',
                    },
                },
                {
                    type: 'item',
                    latex: '\\dagger',
                    icon: 'dagger',
                    label: {
                        en: 'Dagger',
                    },
                },
                {
                    type: 'item',
                    latex: '\\ddagger',
                    icon: 'double-dagger',
                    label: {
                        en: 'Double Dagger',
                    },
                },
                {
                    type: 'item',
                    latex: '\\wr',
                    icon: 'wreath-product',
                    label: {
                        en: 'Wreath Product',
                    },
                },
                {
                    type: 'item',
                    latex: '\\circledast',
                    icon: 'circled-asterisk-operator',
                    label: {
                        en: 'Circled Asterisk Operator',
                    },
                },
                {
                    type: 'item',
                    latex: '\\cap',
                    icon: 'intersection',
                    label: {
                        en: 'Intersection',
                    },
                },
                {
                    type: 'item',
                    latex: '\\cup',
                    icon: 'union',
                    label: {
                        en: 'Union',
                    },
                },
                {
                    type: 'item',
                    latex: '\\Cap',
                    icon: 'double-intersection',
                    label: {
                        en: 'Double Intersection',
                    },
                },
                {
                    type: 'item',
                    latex: '\\Cup',
                    icon: 'double-union',
                    label: {
                        en: 'Double Union',
                    },
                },
                {
                    type: 'item',
                    latex: '\\sqcap',
                    icon: 'square-cap',
                    label: {
                        en: 'Square Cap',
                    },
                },
                {
                    type: 'item',
                    latex: '\\sqcup',
                    icon: 'square-cup',
                    label: {
                        en: 'Square Cup',
                    },
                },
                {
                    type: 'item',
                    latex: '\\wedge',
                    icon: 'logical-and',
                    label: {
                        en: 'Logical And',
                    },
                },
                {
                    type: 'item',
                    latex: '\\vee',
                    icon: 'logical-or',
                    label: {
                        en: 'Logical Or',
                    },
                },
                {
                    type: 'item',
                    latex: '\\unlhd',
                    icon: 'normal-subgroup-of-or-equal-to',
                    label: {
                        en: 'Normal Subgroup Of or Equal To',
                    },
                },
                {
                    type: 'item',
                    latex: '\\unrhd',
                    icon: 'contains-as-normal-subgroup-or-equal-to',
                    label: {
                        en: 'Contains as Normal Subgroup or Equal To',
                    },
                },
                {
                    type: 'item',
                    latex: '\\lhd',
                    icon: 'normal-subgroup-of',
                    label: {
                        en: 'Normal Subgroup Of',
                    },
                },
                {
                    type: 'item',
                    latex: '\\rhd',
                    icon: 'contains-as-normal-subgroup-of',
                    label: {
                        en: 'Contains as Normal Subgroup Of',
                    },
                },
                {
                    type: 'item',
                    latex: '\\diamond',
                    icon: 'diamond',
                    label: {},
                },
                {
                    type: 'item',
                    latex: '\\circ',
                    icon: 'composite-function',
                    label: {
                        en: 'Composite Function',
                    },
                },
                {
                    type: 'item',
                    latex: '\\ominus',
                    icon: 'circled-minus',
                    label: {
                        en: 'Circled Minus',
                    },
                },
                {
                    type: 'item',
                    latex: '\\oplus',
                    icon: 'circled-plus',
                    label: {
                        en: 'Circled Plus',
                    },
                },
                {
                    type: 'item',
                    latex: '\\oslash',
                    icon: 'circle-division-slash',
                    label: {
                        en: 'Circle Division Slash',
                    },
                },
                {
                    type: 'item',
                    latex: '\\circleddash',
                    icon: 'circled-dash',
                    label: {
                        en: 'Circled Dash',
                    },
                },
                {
                    type: 'item',
                    latex: '\\odot',
                    icon: 'circled-dot-operator',
                    label: {
                        en: 'Circled Dot Operator',
                    },
                },
                {
                    type: 'item',
                    latex: '\\cdot',
                    icon: 'dot-operator',
                    label: {
                        en: 'Dot Operator',
                    },
                },
                {
                    type: 'item',
                    latex: '\\uplus',
                    icon: 'multiset-union',
                    label: {
                        en: 'Multiset Union',
                    },
                },
                {
                    type: 'item',
                    latex: '\\bigtriangleup',
                    icon: 'bigtriangleup',
                    label: {},
                },
                {
                    type: 'item',
                    latex: '\\bigtriangledown',
                    icon: 'bigtriangledown',
                    label: {},
                },
                {
                    type: 'item',
                    latex: '\\star',
                    icon: 'star',
                    label: {
                        en: 'Star',
                    },
                },
                {
                    type: 'item',
                    latex: '\\bigstar',
                    icon: 'big-star',
                    label: {
                        en: 'Big Star',
                    },
                },
                {
                    type: 'item',
                    latex: '\\bigcirc',
                    icon: 'bigcirc',
                    label: {
                        en: 'Big Circle',
                    },
                },
                {
                    type: 'item',
                    latex: '\\otimes',
                    icon: 'circled-times',
                    label: {
                        en: 'Circled Times',
                    },
                },
            ],
        },
    ],
};
