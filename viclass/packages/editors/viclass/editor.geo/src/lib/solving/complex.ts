/**
 * A complex number class including many trigonometric functions.
 *
 * This class is immutable, meaning none of its methods will change the
 * real or imaginary components; instead, they return new complex objects.
 *
 * To retrieve the real and imaginary parts of the complex number z use
 * z.real and z.imag
 *
 * Note that most methods can be chained, e.g. `z.tan().sqrt()`.
 *
 * Floating point errors can affect accuracy, so equality checks or
 * real number verification may need tolerance adjustments. The
 * Complex.setEpsilon() method changes the global EPSILON value.
 *
 * Last updated: 17 Jul 2023
 *
 * @ author <PERSON> (2023)
 */

export class Complex {
    static EPSILON: number = 1e-8;
    private static _1_0i = new Complex(1, 0);
    private static _1_0i_neg = new Complex(-1, 0);
    private static _0_1i = new Complex(0, 1);
    private static _0_1i_neg = new Complex(0, -1);

    private _re: number;
    private _im: number;

    /**
     * Constructs a complex number.
     * @param re - real part (default = 0)
     * @param im - imaginary part (default = 0)
     */
    constructor(re: number = 0, im: number = 0) {
        this._re = re;
        this._im = im;
    }

    /**
     * Creates a complex number with specified real and imaginary parts.
     * @param re - real part
     * @param im - imaginary part
     * @returns new Complex instance
     */
    static fromRI(re: number = 0, im: number = 0): Complex {
        return new Complex(re, im);
    }

    /**
     * Returns a copy of an existing complex number.
     * @param z - Complex number to copy
     * @returns new Complex instance identical to input
     */
    static fromZ(z: Complex = new Complex()): Complex {
        return new Complex(z._re, z._im);
    }

    /**
     * Creates a complex number from polar coordinates.
     * @param mod - magnitude
     * @param arg - angle
     * @returns new Complex instance
     */
    static fromPolar(mod: number, arg: number): Complex {
        return new Complex(mod * Math.cos(arg), mod * Math.sin(arg));
    }

    /**
     * Creates a complex number with random orientation and specified magnitude.
     * @param mod - magnitude
     * @returns new Complex instance with random angle
     */
    static fromRandom(mod: number): Complex {
        const arg = 2 * Math.PI * Math.random();
        return new Complex(mod * Math.cos(arg), mod * Math.sin(arg));
    }

    /**
     * Sets maximum tolerance for equality checks.
     * @param epsilon - Tolerance level
     */
    static setEpsilon(epsilon: number): void {
        if (Number.isFinite(epsilon) && epsilon > 0) Complex.EPSILON = epsilon;
    }

    /**
     * Sorts an array of complex numbers by their real and imaginary parts.
     * @param z - array of Complex numbers
     * @returns sorted array
     */
    static sort(z: Complex[]): Complex[] {
        return [...z].sort((a, b) => a.compareTo(b));
    }

    /**
     * Filters complex numbers representing real values.
     * @param z - array of Complex numbers
     * @param epsilon - tolerance level for real number check
     * @returns array with real values only
     */
    static filterRealRoots(z: Complex[], epsilon: number = Complex.EPSILON): Complex[] {
        const nz: Complex[] = [];
        for (const cn of z) if (cn.isReal(epsilon)) nz.push(new Complex(cn.real, 0));
        return nz;
    }

    /**
     * Removes duplicate complex numbers from an array.
     * @param z - array of Complex numbers
     * @param epsilon - tolerance level for equality check
     * @returns array with unique values
     */
    static removeDuplicates(z: Complex[], epsilon: number = Complex.EPSILON): Complex[] {
        if (z.length <= 1) return z;
        const zs = [...z].sort((a, b) => a.compareTo(b));
        const nz: Complex[] = [zs[0]];
        for (let i = 1; i < zs.length; i++) {
            if (!zs[i].equals(nz[nz.length - 1], epsilon)) nz.push(zs[i]);
        }
        return nz;
    }

    /**
     * Gets the real part of the complex number.
     */
    get real(): number {
        return this._re;
    }

    /**
     * Gets the imaginary part of the complex number.
     */
    get imag(): number {
        return this._im;
    }

    /**
     * Calculates the magnitude (absolute value) of the complex number.
     * @returns magnitude
     */
    abs(): number {
        return Math.sqrt(this._re * this._re + this._im * this._im);
    }

    /**
     * Calculates the square of the magnitude.
     * @returns squared magnitude
     */
    abs_squared(): number {
        return this._re * this._re + this._im * this._im;
    }

    /**
     * Adds a number or complex number to this complex number.
     * @param n - a number or Complex instance
     * @returns new Complex instance as sum
     */
    add(n: number | Complex): Complex {
        if (n instanceof Complex) return new Complex(this._re + n._re, this._im + n._im);
        else return new Complex(this._re + n, this._im);
    }

    sub(n: number | Complex) {
        if (n instanceof Complex) return new Complex(this._re - n._re, this._im - n._im);
        else return new Complex(this._re - n, this._im);
    }

    div(n) {
        let re, im;
        if (typeof n === 'object') {
            const m2 = n.abs_squared();
            re = (this._re * n._re + this._im * n._im) / m2;
            im = (this._im * n._re - this._re * n._im) / m2;
        } else {
            re = this._re / n;
            im = this._im / n;
        }
        return new Complex(re, im);
    }

    pow(n: number) {
        return this.log().mult(n).exp();
    }

    exp() {
        const a = Math.exp(this._re);
        const b = Math.cos(this._im);
        const c = Complex._0_1i.mult(Math.sin(this._im)).add(b).mult(a);
        return c;
    }

    cubed() {
        const _re = this._re,
            _im = this.imag;
        const re = _re * _re * _re - 3 * _re * _im * _im;
        const im = 3 * _re * _re * _im - _im * _im * _im;
        return new Complex(re, im);
    }

    roots(n) {
        const roots = [];
        if (Number.isFinite(n) && Number.isInteger(n) && n >= 2) {
            const arg = Math.atan2(this._im, this._re) / n;
            const mod = Math.sqrt(this._re * this._re + this._im * this._im);
            const mod_n = Math.pow(mod, 1 / n);
            const deltaArg = (2 * Math.PI) / n;
            for (let r = 0; r < n; r++) {
                roots.push(Complex.fromPolar(mod_n, arg + r * deltaArg));
            }
        }
        return roots;
    }

    isZero(epsilon = Complex.EPSILON) {
        return Math.abs(this._re) < epsilon && Math.abs(this._im) < epsilon;
    }

    /**
     * Compares this complex number with another for sorting.
     * @param z - complex number to compare
     * @returns -1, 0, or +1
     */
    compareTo(z: Complex): number {
        if (this.equals(z)) return 0;
        else return this._re === z._re ? Math.sign(this._im - z._im) : Math.sign(this._re - z._re);
    }

    /**
     * Calculates the arc cosine of this complex number.
     * @returns new Complex instance
     */
    acos(): Complex {
        const z = this.squared().sub(1).sqrt().add(this).log().mult(Complex._0_1i_neg);
        return z._re < 0 ? z.negate() : z;
    }

    negate() {
        return new Complex(-this._re, -this._im);
    }

    equals(z, epsilon = Complex.EPSILON) {
        return Math.abs(this._re - z._re) < epsilon && Math.abs(this._im - z._im) < epsilon;
    }

    mult(n: number | Complex) {
        let re, im;
        if (n instanceof Complex) {
            re = this._re * n._re - this._im * n._im;
            im = this._re * n._im + this._im * n._re;
        } else {
            re = this._re * n;
            im = this._im * n;
        }
        return new Complex(re, im);
    }

    log() {
        const re = Math.log(this.abs());
        const im = this._re < 0 && this._im == 0 ? Math.PI : this.arg();
        return new Complex(re, im);
    }

    squared() {
        const re = this._re * this._re - this._im * this._im;
        const im = 2 * this._re * this._im;
        return new Complex(re, im);
    }

    sqrt() {
        const abs = this.abs();
        const re = Math.sqrt(0.5 * (abs + this._re));
        let im = Math.sqrt(0.5 * (abs - this._re));
        if (this._im < 0) im *= -1;
        return new Complex(re, im);
    }

    /**
     * Calculates the argument (angle) of the complex number.
     * @returns angle in radians
     */
    arg(): number {
        return Math.atan2(this._im, this._re);
    }

    isReal(epsilon = Complex.EPSILON) {
        return Math.abs(this._im) <= epsilon;
    }

    /**
     * Creates a clone of this complex number.
     * @returns new Complex instance identical to this
     */
    clone(): Complex {
        return new Complex(this._re, this._im);
    }

    /**
     * Prints the complex number to console.
     * @param precision - number of significant digits
     * @returns this instance
     */
    print(precision: number = 16): this {
        console.log(this.$(precision));
        return this;
    }

    /**
     * Returns a formatted string of the complex number.
     * @param precision - number of significant digits
     * @returns string representation
     */
    $(precision: number = 16): string {
        const sgn = this._im < 0 ? '-' : '+';
        const rep = this._re.toPrecision(precision);
        const imp = Math.abs(this._im).toPrecision(precision);
        const s = `[ ${rep} ${sgn} ${imp}i ]`;
        return s;
    }

    /**
     * Converts the complex number to a string.
     * @returns string representation
     */
    toString(): string {
        return this.$();
    }
}
