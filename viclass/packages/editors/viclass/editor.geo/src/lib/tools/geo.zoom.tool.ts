import {
    DefaultMouseEventData,
    MouseEventData,
    mouseLocation,
    NativeEventTarget,
    Position,
    ZoomFeature,
} from '@viclass/editor.core';
import { debounceTime, Subject } from 'rxjs';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { GeoZoomToolState, UpdatePropToolState } from '../model';
import { GeometryToolType } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { GeometryTool } from './geo.tool';
import { calculatePosInLayer, validatePointerPos } from './util.tool';

export class GeoZoomTool extends GeometryTool<GeoZoomToolState> {
    private submitServer: Subject<GeoDocCtrl> = new Subject<GeoDocCtrl>();

    constructor(
        editor: GeometryEditor,
        toolbar: GeometryToolBar,
        private zoomFeature: ZoomFeature
    ) {
        super(editor, toolbar);

        this.submitServer.pipe(debounceTime(300)).subscribe(docCtrl => {
            const toolState: UpdatePropToolState = this.toolbar.toolState('UpdatePropTool');
            toolState.docRenderProp = docCtrl.state.docRenderProp;
            toolState.notProcess = false;
            this.toolbar.update('UpdatePropTool', toolState);
        });
    }

    clear() {
        this.started = false;
        this.lastPointerMove = undefined;
    }

    override onFocus() {
        this.clear();
    }

    override resetState() {
        this.clear();
    }

    get toolType(): GeometryToolType {
        return 'GeoZoomTool';
    }

    override onAttachViewport() {
        this.zoomFeature.registerZoomHandler(this.toolbar.viewport.id, this.editor.editorType, this);
    }

    isZoomHandleAble(docCtrl: GeoDocCtrl, event: MouseEventData<NativeEventTarget<any>>): boolean {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return false;
        try {
            if (event instanceof DefaultMouseEventData) {
                const mousePos = mouseLocation(event);
                if (!validatePointerPos(mousePos, docCtrl)) {
                    return false;
                }
                return true;
            }
        } catch (error) {
            return false;
        }

        return false;
    }

    onMouseZoom(
        docCtrl: GeoDocCtrl,
        event: MouseEventData<NativeEventTarget<any>>
    ): MouseEventData<NativeEventTarget<any>> | Promise<MouseEventData<NativeEventTarget<any>>> {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return event;
        if (event.eventType === 'mousewheel' || event.eventType === 'wheel') {
            const docRenderProp = docCtrl.state.docRenderProp;
            const zoomLevel = docRenderProp.scale;
            const space = docCtrl.rendererCtrl.geoToLayerLength(1) / 10;
            const zoomIntensity = space / docRenderProp.screenUnit;
            const wheel = (event.nativeEvent as WheelEvent).deltaY < 0 ? 1 : -1;
            const maxZoom = 5;
            const minZoom = 0.25;

            let zoom = zoomLevel + wheel * zoomIntensity;
            if (zoom >= maxZoom) zoom = maxZoom;
            if (zoom <= minZoom) zoom = minZoom;

            const mousePos: Position = mouseLocation(event);
            const mousePosInLayer = calculatePosInLayer(mousePos, docCtrl);
            const mousePosInGeo = docCtrl.rendererCtrl.layerToGeoPos(mousePosInLayer);

            docCtrl.rendererCtrl.zoom(zoom, mousePosInGeo);

            // update toolstate
            this.toolbar.update('GeoZoomTool', this.toolState);

            event.continue = false;

            this.submitServer.next(docCtrl);
        }
        return event;
    }

    onTouchZoom(docCtrl: GeoDocCtrl, mousePos: Position, wheel: 1 | -1) {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return;

        const docRenderProp = docCtrl.state.docRenderProp;
        const zoomLevel = docRenderProp.scale;
        const space = docCtrl.rendererCtrl.geoToLayerLength(1);
        const zoomIntensity = space / docRenderProp.screenUnit;
        const maxZoom = 100;
        const minZoom = 0.1 / docRenderProp.screenUnit;
        let zoom = zoomLevel + wheel * zoomIntensity;

        if (zoom >= maxZoom) zoom = maxZoom;
        if (zoom <= minZoom) zoom = minZoom;

        const mousePosInLayer = calculatePosInLayer(mousePos, docCtrl);
        const mousePosInGeo = docCtrl.rendererCtrl.layerToGeoPos(mousePosInLayer);

        docCtrl.rendererCtrl.zoom(zoom, mousePosInGeo);

        // update toolstate
        this.toolbar.update('GeoZoomTool', this.toolState);

        this.submitServer.next(docCtrl);
    }
}
