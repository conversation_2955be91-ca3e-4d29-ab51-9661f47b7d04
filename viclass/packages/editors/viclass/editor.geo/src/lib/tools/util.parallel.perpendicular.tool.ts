import { point } from '@flatten-js/core';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoRenderElement,
    RenderCircle,
    RenderEllipse,
    RenderLine,
    RenderSector,
    RenderVertex,
    StrokeType,
} from '../model';
import { GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { or, strk, stroke, then, ThenSelector, vert, vertex, VertexOnStroke, vertexOnStroke } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    calculateLineCircleIntersection,
    calculateLineLineIntersection,
    intersectionLineEllipse,
    isPointInSector,
} from './util.intersections';
import {
    assignNames,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isElementLine,
    projectPointOntoLine,
    remoteConstruct,
} from './util.tool';
import { createFlattenLine } from './util.flatten';

/**
 * Base class for line-based geometry tools (parallel, perpendicular, etc.)
 * Contains shared functionality for tools that create lines based on existing lines and points
 */
export abstract class BaseParallelPerpendicularTool extends GeometryTool<CommonToolState> {
    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    selectedLine: RenderLine | undefined;
    selectedCurvedElement: RenderCircle | RenderEllipse | RenderSector | undefined;
    selectedPoint: RenderVertex | undefined;
    previewLine: RenderLine | undefined;
    directionVector: number[] | undefined;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.selectedLine = undefined;
        this.selectedCurvedElement = undefined;
        this.selectedPoint = undefined;
        this.previewLine = undefined;
        this.directionVector = undefined;
        super.resetState();
    }

    /**
     * Abstract methods that must be implemented by subclasses
     */
    protected abstract createLinePreview(ctrl: GeoDocCtrl, line: RenderLine, throughPoint: RenderVertex): void;

    protected abstract createCurvedElementPreview(
        ctrl: GeoDocCtrl,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        tangentPoint: RenderVertex
    ): void;

    protected abstract buildSimpleLineConstruction(
        lineName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex
    ): any;

    protected abstract buildSimpleCurvedElementConstruction(
        lineName: string,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        tangentPoint: RenderVertex
    ): any;

    protected abstract buildLineSegmentConstruction(
        combinedName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        scalingFactor: number
    ): any;

    protected abstract buildLineSegmentWithIntersectionConstruction(
        combinedName: string,
        baseLine: RenderLine,
        intersectLine: RenderLine,
        throughPoint: RenderVertex
    ): any;

    protected abstract buildCurvedElementConstruction(
        lineName: string,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        tangentPoint: RenderVertex,
        intersectionOrder?: number
    ): any;

    protected abstract getSimpleConstructionLabel(): string;

    protected abstract getComplexConstructionLabel(): string;

    /**
     * Creates the selection logic using selector pattern with preview
     * Following Pattern: Line/Curved Element -> First Vertex -> Final Vertex/Vertex on Stroke
     */
    protected createSelLogic() {
        // First selector: select a line or curved element
        const elementSelector = stroke({
            selectableStrokeTypes: ['RenderVector', 'RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderCircle', 'RenderEllipse', 'RenderSector'],
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Second selector: select first vertex to define through which the line passes
        const firstVertexSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
            onComplete: (selector, doc) => {
                // Create preview line after first vertex is selected
                const selectedElement = strk(elementSelector.selected as any);
                const firstVertex = vert(selector.selected as RenderVertex);

                if (isElementLine(selectedElement)) {
                    this.selectedLine = selectedElement as RenderLine;
                    this.selectedPoint = firstVertex;
                    this.createLinePreview(doc, this.selectedLine, this.selectedPoint);
                } else if (selectedElement.type === 'RenderCircle' || selectedElement.type === 'RenderEllipse' || selectedElement.type === 'RenderSector') {
                    this.selectedCurvedElement = selectedElement as RenderCircle | RenderEllipse | RenderSector;
                    this.selectedPoint = firstVertex;
                    // For curved elements, we need to create preview based on tangent at the selected point
                    this.createCurvedElementPreview(doc, this.selectedCurvedElement, this.selectedPoint, this.selectedPoint);
                }
            },
        });

        // Third selector: select final vertex or vertex on stroke
        const finalVertexSelector = or(
            [
                // Option 1: Select existing vertex with validation and projection
                vertex({
                    preview: true,
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    cfunc: (el: RenderVertex, doc: GeoDocCtrl) => this.validateFinalVertex(el, doc),
                    tfunc: (previewEl: RenderVertex, doc: GeoDocCtrl) => this.projectFinalVertex(previewEl, doc),
                }),
                // Option 2: Select vertex on stroke with intersection projection
                vertexOnStroke({
                    preview: true,
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    cfunc: (stroke, doc) => this.checkFinalVertexOnStroke(stroke, doc),
                    tfunc: (stroke, previewVertex, doc) => this.projectFinalVertexOnStroke(stroke, previewVertex, doc),
                    refinedFilter: (el: GeoRenderElement) =>
                        isElementLine(el) ||
                        el.type === 'RenderCircle' ||
                        el.type === 'RenderEllipse' ||
                        el.type === 'RenderSector',
                }),
            ],
            { flatten: true }
        );

        // Main selection logic: element -> first vertex -> final vertex
        this.selLogic = then([elementSelector, firstVertexSelector, finalVertexSelector], {
            onComplete: async (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [element, firstVertex, finalVertexSelection] = selector.selected;

                const selectedElement = strk(element as any);
                this.selectedPoint = vert(firstVertex as RenderVertex);

                // Handle both line-based and curved element construction
                if (isElementLine(selectedElement)) {
                    this.selectedLine = selectedElement as RenderLine;
                    const finalVertex = vert(finalVertexSelection as RenderVertex | VertexOnStroke);

                    if (this.selectedPoint.relIndex === finalVertex.relIndex) {
                        if (!this.previewLine) {
                            console.error('Preview line not available for same point construction');
                            this.resetState();
                            return;
                        }
                        await this.handleSimpleLineConstruction(doc, this.selectedLine, this.selectedPoint);
                    } else {
                        await this.handleComplexLineConstruction(
                            doc,
                            this.selectedLine,
                            this.selectedPoint,
                            finalVertex,
                            finalVertexSelection
                        );
                    }
                } else if (selectedElement.type === 'RenderCircle' || selectedElement.type === 'RenderEllipse' || selectedElement.type === 'RenderSector') {
                    // Handle curved element construction
                    this.selectedCurvedElement = selectedElement as RenderCircle | RenderEllipse | RenderSector;
                    const finalVertex = vert(finalVertexSelection as RenderVertex | VertexOnStroke);

                    await this.handleCurvedElementConstruction(
                        doc,
                        this.selectedCurvedElement,
                        this.selectedPoint,
                        finalVertex
                    );
                } else {
                    console.error('Unsupported element type for construction:', selectedElement.type);
                    this.resetState();
                    return;
                }

                this.resetState();
            },
        });
    }

    /**
     * Validates final vertex selection according to new requirements:
     * - If relIndex >= 0 (real vertex): only allow if it belongs to preview line
     * - If relIndex < 0 (preview vertex): always allow (will be projected)
     */
    private validateFinalVertex(el: RenderVertex, doc: GeoDocCtrl): boolean {
        // Always allow preview vertices (relIndex < 0) - they will be projected
        if (el.relIndex < 0) return true;

        // For real vertices (relIndex >= 0), only allow if they belong to the preview line
        if (!this.previewLine) return false;

        try {
            // Check if the vertex lies on the preview line with some tolerance
            const pt = point(el.coords[0], el.coords[1]);
            const fl = createFlattenLine(this.previewLine, doc);

            // Calculate distance from point to line
            const distance = fl.distanceTo(pt)[0];
            const tolerance = 1e-6; // Small tolerance for floating point precision

            return distance < tolerance;
        } catch (error) {
            console.warn('Error validating final vertex:', error);
            return false;
        }
    }

    /**
     * Projects final vertex according to new requirements:
     * - If relIndex >= 0 (real vertex): return as-is (already validated to be on preview line)
     * - If relIndex < 0 (preview vertex): project onto preview line
     */
    protected projectFinalVertex(previewEl: RenderVertex, _doc: GeoDocCtrl): RenderVertex {
        // For real vertices (relIndex >= 0), return as-is since they're already validated
        if (previewEl.relIndex >= 0) return previewEl;

        // For preview vertices (relIndex < 0), project onto preview line
        if (!this.selectedPoint || !this.directionVector) {
            console.warn('Missing selectedPoint or directionVector for projection');
            return previewEl;
        }

        try {
            const projectedCoords = projectPointOntoLine(
                previewEl.coords,
                this.selectedPoint.coords,
                this.directionVector
            );
            previewEl.coords[0] = projectedCoords[0];
            previewEl.coords[1] = projectedCoords[1];
            return previewEl;
        } catch (error) {
            console.warn('Error projecting point onto line:', error);
            return previewEl;
        }
    }

    /**
     * Check function to validate that stroke has intersection with preview line
     * or contains the selected point
     */
    protected checkFinalVertexOnStroke(stroke: StrokeType, doc: GeoDocCtrl): boolean {
        if (!this.previewLine) return false;

        const s = strk(stroke);

        if (s.relIndex === this.previewLine.relIndex) return true;

        try {
            let intersections: any[] = [];

            // Calculate intersections based on stroke type
            if (isElementLine(stroke))
                intersections = calculateLineLineIntersection(this.previewLine, stroke as RenderLine, doc);
            else if (stroke.type === 'RenderCircle')
                intersections = calculateLineCircleIntersection(this.previewLine, stroke as RenderCircle, doc);
            else if (stroke.type === 'RenderEllipse')
                intersections = intersectionLineEllipse(this.previewLine, stroke as RenderEllipse, doc);
            else if (stroke.type === 'RenderSector') {
                const sector = stroke as RenderSector;

                // Create a temporary circle for intersection calculation
                const tempCircle = new RenderCircle();
                tempCircle.centerPointIdx = sector.centerPointIdx;
                tempCircle.radius = sector.radius;

                intersections = calculateLineCircleIntersection(this.previewLine, tempCircle, doc);

                // Filter intersections to only include points that are within the sector
                if (intersections && intersections.length > 0) {
                    intersections = intersections.filter(intersection => {
                        const pt = point(intersection.x, intersection.y);
                        return isPointInSector(pt, sector, doc);
                    });
                }
            }

            return intersections && intersections.length > 0;
        } catch (error) {
            console.warn('Error checking stroke intersection:', error);
            return false;
        }
    }

    /**
     * Transform function to project vertex on stroke to intersection with line
     */
    protected projectFinalVertexOnStroke(
        stroke: StrokeType,
        previewVertex: RenderVertex,
        doc: GeoDocCtrl
    ): RenderVertex {
        if (!this.previewLine) return previewVertex;

        const s = stroke as RenderLine;

        if (this.previewLine.relIndex === s.relIndex) return previewVertex;

        try {
            let intersections: any[] = [];

            // Calculate intersections based on stroke type
            if (isElementLine(stroke)) {
                intersections = calculateLineLineIntersection(this.previewLine, stroke as RenderLine, doc);
            } else if (stroke.type === 'RenderCircle') {
                intersections = calculateLineCircleIntersection(this.previewLine, stroke as RenderCircle, doc);
            } else if (stroke.type === 'RenderEllipse') {
                intersections = intersectionLineEllipse(this.previewLine, stroke as RenderEllipse, doc);
            } else if (stroke.type === 'RenderSector') {
                const sector = stroke as RenderSector;

                // Create a temporary circle for intersection calculation
                const tempCircle = new RenderCircle();
                tempCircle.centerPointIdx = sector.centerPointIdx;
                tempCircle.radius = sector.radius;

                intersections = calculateLineCircleIntersection(this.previewLine, tempCircle, doc);

                // Filter intersections to only include points that are within the sector
                if (intersections && intersections.length > 0) {
                    intersections = intersections.filter(intersection => {
                        const pt = point(intersection.x, intersection.y);
                        return isPointInSector(pt, sector, doc);
                    });
                }
            }

            if (intersections?.length) {
                // For multiple intersections (circle, ellipse, sector), find the closest one to current position
                if (
                    intersections.length > 1 &&
                    (stroke.type === 'RenderCircle' ||
                        stroke.type === 'RenderEllipse' ||
                        stroke.type === 'RenderSector')
                ) {
                    const currentPos = point(previewVertex.coords[0], previewVertex.coords[1]);
                    let closestIntersection = intersections[0];
                    let minDistance = currentPos.distanceTo(point(intersections[0].x, intersections[0].y))[0];

                    for (let i = 1; i < intersections.length; i++) {
                        const intersectionPoint = point(intersections[i].x, intersections[i].y);
                        const distance = currentPos.distanceTo(intersectionPoint)[0];

                        if (distance < minDistance) {
                            minDistance = distance;
                            closestIntersection = intersections[i];
                        }
                    }

                    previewVertex.coords = [closestIntersection.x, closestIntersection.y];
                } else {
                    // For single intersection or line intersections, use the first intersection
                    previewVertex.coords = [intersections[0].x, intersections[0].y];
                }
            } else {
                return undefined;
            }
        } catch (error) {
            console.warn('Error projecting vertex on stroke to intersection:', error);
        }

        return previewVertex;
    }

    /**
     * Handle simple line construction when final vertex is same as through point
     */
    protected async handleSimpleLineConstruction(ctrl: GeoDocCtrl, line: RenderLine, throughPoint: RenderVertex) {
        try {
            // Validate essential prerequisites
            if (!this.previewLine) {
                console.error('previewLine is not available for construction');
                this.resetState();
                return;
            }

            // Use assignNames with previewLine as target object
            await assignNames(
                ctrl,
                [],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                '',
                this.getSimpleConstructionLabel(),
                this.previewLine
            );

            // Use build line construction
            const construction = this.buildSimpleLineConstruction(this.previewLine.name, line, throughPoint);

            await remoteConstruct(ctrl, construction, [], this.editor.geoGateway, this.getSimpleConstructionLabel());
        } catch (error) {
            console.error('Error in simple line construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    /**
     * Handle complex line construction when final vertex is different from through point
     */
    protected async handleComplexLineConstruction(
        ctrl: GeoDocCtrl,
        line: RenderLine,
        throughPoint: RenderVertex,
        finalVertex: RenderVertex,
        finalVertexSelection: any
    ) {
        try {
            // Line segment to point - assign names for both line and endpoint
            const { pcs, points } = await assignNames(
                ctrl,
                [throughPoint, finalVertex],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                'Tên điểm cuối',
                this.getComplexConstructionLabel()
            );

            if (!pcs || !points) {
                this.resetState();
                return;
            }

            throughPoint.name = points.find(p => p.relIndex === throughPoint.relIndex)?.name;
            finalVertex.name = points.find(p => p.relIndex === finalVertex.relIndex)?.name;

            // Create combined name for through point and end element
            const combinedName = `${throughPoint.name}${finalVertex.name}`;

            // Check if final vertex is from a vertex on stroke
            // VertexOnStroke has structure [StrokeType, RenderVertex]
            const isVertexOnStroke = Array.isArray(finalVertexSelection) && finalVertexSelection.length === 2;

            if (isVertexOnStroke) {
                // For VertexOnStroke, finalVertexSelection is [StrokeType, RenderVertex]
                const stroke = finalVertexSelection[0] as StrokeType;

                // Check if stroke is a line or curved element
                if (isElementLine(stroke)) {
                    // For line strokes, check conditions for segment vs intersection construction
                    const strokeBelongsToPreviewLine = stroke.relIndex === this.previewLine?.relIndex;
                    const vertexIsNotFirstVertex = finalVertex.relIndex !== this.selectedPoint.relIndex;

                    if (strokeBelongsToPreviewLine || vertexIsNotFirstVertex) {
                        // Use segment construction with scaling factor
                        const startPt = point(throughPoint.coords[0], throughPoint.coords[1]);
                        const endPt = point(finalVertex.coords[0], finalVertex.coords[1]);

                        let k = 0;
                        if (startPt && endPt && this.directionVector) {
                            // Calculate vector from through point to final vertex
                            const toEndVector = [
                                finalVertex.coords[0] - throughPoint.coords[0],
                                finalVertex.coords[1] - throughPoint.coords[1],
                            ];

                            // Calculate dot product to determine direction
                            const dotProduct =
                                toEndVector[0] * this.directionVector[0] + toEndVector[1] * this.directionVector[1];

                            // Distance from through point to final vertex
                            const distance = startPt.distanceTo(endPt)[0];

                            // k is positive if same direction, negative if opposite direction
                            k = dotProduct >= 0 ? distance : -distance;
                        }

                        const construction = this.buildLineSegmentConstruction(combinedName, line, throughPoint, k);
                        await remoteConstruct(
                            ctrl,
                            construction,
                            pcs.filter(pc => pc.name === throughPoint.name),
                            this.editor.geoGateway,
                            this.getComplexConstructionLabel()
                        );
                    } else {
                        // Use intersection construction for line strokes that don't belong to preview line
                        const construction = this.buildLineSegmentWithIntersectionConstruction(
                            combinedName,
                            line,
                            stroke as RenderLine,
                            throughPoint
                        );
                        await remoteConstruct(
                            ctrl,
                            construction,
                            pcs.filter(pc => pc.name === throughPoint.name),
                            this.editor.geoGateway,
                            this.getComplexConstructionLabel()
                        );
                    }
                } else {
                    // For curved element strokes (circle, ellipse, sector), use intersection construction
                    const curvedElement = stroke as RenderCircle | RenderEllipse | RenderSector;

                    // Use curved element intersection construction
                    const construction = this.buildCurvedElementConstruction(
                        combinedName,
                        curvedElement,
                        throughPoint,
                        finalVertex, // This is just for compatibility, not used as tangent
                        0 // Default intersection order, could be enhanced to calculate proper order
                    );

                    await remoteConstruct(
                        ctrl,
                        construction,
                        pcs.filter(pc => pc.name === throughPoint.name),
                        this.editor.geoGateway,
                        this.getComplexConstructionLabel()
                    );
                }
            } else {
                // For regular vertex selection (not vertex on stroke), use segment construction
                const startPt = point(throughPoint.coords[0], throughPoint.coords[1]);
                const endPt = point(finalVertex.coords[0], finalVertex.coords[1]);

                let k = 0;
                if (startPt && endPt && this.directionVector) {
                    // Calculate vector from through point to final vertex
                    const toEndVector = [
                        finalVertex.coords[0] - throughPoint.coords[0],
                        finalVertex.coords[1] - throughPoint.coords[1],
                    ];

                    // Calculate dot product to determine direction
                    const dotProduct =
                        toEndVector[0] * this.directionVector[0] + toEndVector[1] * this.directionVector[1];

                    // Distance from through point to final vertex
                    const distance = startPt.distanceTo(endPt)[0];

                    // k is positive if same direction, negative if opposite direction
                    k = dotProduct >= 0 ? distance : -distance;
                }

                const construction = this.buildLineSegmentConstruction(combinedName, line, throughPoint, k);
                await remoteConstruct(
                    ctrl,
                    construction,
                    pcs.filter(pc => pc.name === throughPoint.name),
                    this.editor.geoGateway,
                    this.getComplexConstructionLabel()
                );
            }
        } catch (error) {
            console.error('Error in complex line construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    /**
     * Handle simple curved element construction when final vertex is same as through point
     */
    protected async handleSimpleCurvedElementConstruction(
        ctrl: GeoDocCtrl,
        throughPoint: RenderVertex,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        tangentPoint: RenderVertex
    ) {
        try {
            // Validate essential prerequisites
            if (!this.previewLine) {
                console.error('previewLine is not available for construction');
                this.resetState();
                return;
            }

            // Use assignNames with previewLine as target object
            await assignNames(
                ctrl,
                [],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                '',
                this.getSimpleConstructionLabel(),
                this.previewLine
            );

            // Use build curved element construction
            const construction = this.buildSimpleCurvedElementConstruction(
                this.previewLine.name,
                curvedElement,
                throughPoint,
                tangentPoint
            );

            await remoteConstruct(ctrl, construction, [], this.editor.geoGateway, this.getSimpleConstructionLabel());
        } catch (error) {
            console.error('Error in simple curved element construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    /**
     * Handle complex curved element construction when final vertex is different from through point
     */
    protected async handleComplexCurvedElementConstruction(
        ctrl: GeoDocCtrl,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        tangentPoint: RenderVertex,
        finalVertex: RenderVertex,
        finalVertexSelection: any
    ) {
        try {
            // Line segment to point - assign names for both line and endpoint
            const { pcs, points } = await assignNames(
                ctrl,
                [throughPoint, finalVertex],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                'Tên điểm cuối',
                this.getComplexConstructionLabel()
            );

            if (!pcs || !points) {
                this.resetState();
                return;
            }

            throughPoint.name = points.find(p => p.relIndex === throughPoint.relIndex)?.name;
            finalVertex.name = points.find(p => p.relIndex === finalVertex.relIndex)?.name;
            tangentPoint.name = tangentPoint.name || 'T'; // Default tangent point name

            // Create combined name for through point and end element
            const combinedName = `${throughPoint.name}${finalVertex.name}`;

            // For curved elements, we typically use segment construction with scaling factor
            const startPt = point(throughPoint.coords[0], throughPoint.coords[1]);
            const endPt = point(finalVertex.coords[0], finalVertex.coords[1]);

            let k = 0;
            if (startPt && endPt && this.directionVector) {
                // Calculate vector from through point to final vertex
                const toEndVector = [
                    finalVertex.coords[0] - throughPoint.coords[0],
                    finalVertex.coords[1] - throughPoint.coords[1],
                ];

                // Calculate dot product to determine direction
                const dotProduct = toEndVector[0] * this.directionVector[0] + toEndVector[1] * this.directionVector[1];

                // Distance from through point to final vertex
                const distance = startPt.distanceTo(endPt)[0];

                // k is positive if same direction, negative if opposite direction
                k = dotProduct >= 0 ? distance : -distance;
            }

            // For now, use simple construction - can be extended later for segment construction
            const construction = this.buildSimpleCurvedElementConstruction(
                combinedName,
                curvedElement,
                throughPoint,
                tangentPoint
            );

            await remoteConstruct(
                ctrl,
                construction,
                pcs.filter(pc => pc.name === throughPoint.name),
                this.editor.geoGateway,
                this.getComplexConstructionLabel()
            );
        } catch (error) {
            console.error('Error in complex curved element construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') if (!this.shouldHandleClick(event)) return event;

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, (event: any) =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    protected doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        this.selLogic.trySelect(event, ctrl);

        // Show preview based on current selection state
        if (this.selLogic.selected && Array.isArray(this.selLogic.selected)) {
            const selections = this.selLogic.selected;
            const [element, firstVertex, finalSelection] = selections;

            if (element && firstVertex && !finalSelection) {
                // After selecting element and first vertex, show preview line
                const selectedElement = strk(element as any);
                this.selectedPoint = vert(firstVertex as RenderVertex);

                if (isElementLine(selectedElement)) {
                    // For lines: element -> firstVertex -> finalVertex
                    this.selectedLine = selectedElement as RenderLine;
                    this.createLinePreview(ctrl, this.selectedLine, this.selectedPoint);
                } else {
                    // Curved elements not supported in simplified selection logic
                    console.warn('Curved element selection not supported in simplified selection logic');
                }
            }
        } else if (this.selLogic.selected && !Array.isArray(this.selLogic.selected)) {
            // Only element selected (not array yet)
            const element = this.selLogic.selected as any;
            if (
                element &&
                [
                    'RenderLine',
                    'RenderLineSegment',
                    'RenderRay',
                    'RenderVector',
                    'RenderCircle',
                    'RenderEllipse',
                    'RenderSector',
                ].includes(element.type)
            ) {
                if (isElementLine(element)) {
                    this.selectedLine = element as RenderLine;
                } else {
                    this.selectedCurvedElement = element as RenderCircle | RenderEllipse | RenderSector;
                }
            }
        }

        // Always flush at the end
        this.pQ.flush(ctrl);
    }

    /**
     * Calculate tangent direction vector for a circle at a given point
     */
    protected calculateCircleTangentVector(
        circle: RenderCircle,
        tangentPoint: RenderVertex,
        ctrl: GeoDocCtrl
    ): number[] {
        const centerCoords = circle.coord('center', ctrl.rendererCtrl);
        const pointCoords = tangentPoint.coords;

        // Vector from center to point
        const radiusVector = [pointCoords[0] - centerCoords[0], pointCoords[1] - centerCoords[1]];

        // Tangent vector is perpendicular to radius vector
        const tangentVector = [-radiusVector[1], radiusVector[0]];

        // Normalize the vector
        const magnitude = Math.sqrt(tangentVector[0] ** 2 + tangentVector[1] ** 2);
        if (magnitude === 0) throw new Error('Circle tangent vector has zero magnitude');

        return [tangentVector[0] / magnitude, tangentVector[1] / magnitude];
    }

    /**
     * Calculate tangent direction vector for an ellipse at a given point
     */
    protected calculateEllipseTangentVector(
        ellipse: RenderEllipse,
        tangentPoint: RenderVertex,
        ctrl: GeoDocCtrl
    ): number[] {
        // Get ellipse parameters
        const centerCoords = ellipse.coord('center', ctrl.rendererCtrl);
        const pointCoords = tangentPoint.coords;

        // For simplicity, approximate ellipse tangent using the same approach as circle
        // This is a simplified implementation - a more accurate version would require
        // the ellipse's rotation angle and semi-major/minor axes
        const radiusVector = [pointCoords[0] - centerCoords[0], pointCoords[1] - centerCoords[1]];
        const tangentVector = [-radiusVector[1], radiusVector[0]];

        // Normalize the vector
        const magnitude = Math.sqrt(tangentVector[0] ** 2 + tangentVector[1] ** 2);
        if (magnitude === 0) throw new Error('Ellipse tangent vector has zero magnitude');

        return [tangentVector[0] / magnitude, tangentVector[1] / magnitude];
    }

    /**
     * Calculate tangent direction vector for a sector at a given point
     */
    protected calculateSectorTangentVector(
        sector: RenderSector,
        tangentPoint: RenderVertex,
        ctrl: GeoDocCtrl
    ): number[] {
        // For sectors, use the same approach as circles since sectors are portions of circles
        const centerCoords = sector.coord('center', ctrl.rendererCtrl);
        const pointCoords = tangentPoint.coords;

        // Vector from center to point
        const radiusVector = [pointCoords[0] - centerCoords[0], pointCoords[1] - centerCoords[1]];

        // Tangent vector is perpendicular to radius vector
        const tangentVector = [-radiusVector[1], radiusVector[0]];

        // Normalize the vector
        const magnitude = Math.sqrt(tangentVector[0] ** 2 + tangentVector[1] ** 2);
        if (magnitude === 0) throw new Error('Sector tangent vector has zero magnitude');

        return [tangentVector[0] / magnitude, tangentVector[1] / magnitude];
    }

    /**
     * Calculate direction vector for curved elements
     */
    protected calculateCurvedElementDirectionVector(
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        tangentPoint: RenderVertex,
        ctrl: GeoDocCtrl
    ): number[] {
        switch (curvedElement.type) {
            case 'RenderCircle':
                return this.calculateCircleTangentVector(curvedElement as RenderCircle, tangentPoint, ctrl);
            case 'RenderEllipse':
                return this.calculateEllipseTangentVector(curvedElement as RenderEllipse, tangentPoint, ctrl);
            case 'RenderSector':
                return this.calculateSectorTangentVector(curvedElement as RenderSector, tangentPoint, ctrl);
            default:
                throw new Error(`Unsupported curved element type: ${(curvedElement as any).type}`);
        }
    }

    /**
     * Handle construction for curved elements (circles, ellipses, sectors)
     */
    protected async handleCurvedElementConstruction(
        ctrl: GeoDocCtrl,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        tangentPoint: RenderVertex
    ) {
        try {
            // Use assignNames with curvedElement as target object
            await assignNames(
                ctrl,
                [],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                [curvedElement],
                async (names: string[]) => {
                    const lineName = names[0];

                    // Use simple curved element construction
                    const construction = this.buildSimpleCurvedElementConstruction(
                        lineName,
                        curvedElement,
                        throughPoint,
                        tangentPoint
                    );

                    await remoteConstruct(
                        ctrl,
                        construction,
                        [],
                        this.editor.geoGateway,
                        this.getSimpleConstructionLabel()
                    );
                }
            );
        } catch (error) {
            console.error('Error in curved element construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }
}
