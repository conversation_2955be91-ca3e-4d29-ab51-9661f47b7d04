import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { RenderLine, RenderVertex } from '../model';
import { GeometryToolType } from '../model/geo.models';
import { pLine } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import {
    buildPerpendicularLine,
    buildPerpendicularLineSegment,
    buildPerpendicularLineSegmentWithIntersectLine,
} from './util.construction';
import { BaseParallelPerpendicularTool } from './util.parallel.perpendicular.tool';

/**
 * Perpendicular Line Tool - Creates perpendicular lines using selector pattern with preview
 * <AUTHOR>
 */
export class CreatePerpendicularLineTool extends BaseParallelPerpendicularTool {
    readonly toolType: GeometryToolType = 'CreatePerpendicularLineTool';

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
    }

    /**
     * Calculate perpendicular vector from the selected line
     */
    protected calculateDirectionVector(line: RenderLine, ctrl: GeoDocCtrl): number[] {
        // Get line direction vector
        const lineVector = line.orderedVector(ctrl.rendererCtrl);
        if (!lineVector) throw new Error('Could not get direction vector from line');

        // Calculate perpendicular vector (rotate 90 degrees)
        // If line vector is [a, b], perpendicular vector is [-b, a]
        const perpVector = [-lineVector[1], lineVector[0]];

        // Normalize the vector
        const magnitude = Math.sqrt(perpVector[0] ** 2 + perpVector[1] ** 2);
        if (magnitude === 0) throw new Error('Perpendicular vector has zero magnitude');

        return [perpVector[0] / magnitude, perpVector[1] / magnitude];
    }

    /**
     * Show perpendicular line preview
     */
    protected createLinePreview(ctrl: GeoDocCtrl, line: RenderLine, throughPoint: RenderVertex): void {
        try {
            // Calculate direction vector
            this.directionVector = this.calculateDirectionVector(line, ctrl);

            // Create perpendicular line preview passing through the selected point
            this.previewLine = pLine(ctrl, -20, RenderLine, throughPoint, undefined, this.directionVector);

            // Add to preview queue
            this.pQ.add(this.previewLine);
        } catch (error) {
            console.warn('Error creating perpendicular line preview:', error);
        }
    }

    protected buildSimpleLineConstruction(lineName: string, baseLine: RenderLine, throughPoint: RenderVertex): any {
        return buildPerpendicularLine(lineName, baseLine.name, baseLine.elType, throughPoint.name);
    }

    protected buildLineSegmentConstruction(
        combinedName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        scalingFactor: number
    ): any {
        return buildPerpendicularLineSegment(
            combinedName,
            baseLine.name,
            baseLine.elType,
            throughPoint.name,
            scalingFactor
        );
    }

    protected buildLineSegmentWithIntersectionConstruction(
        combinedName: string,
        baseLine: RenderLine,
        intersectLine: RenderLine,
        throughPoint: RenderVertex
    ): any {
        return buildPerpendicularLineSegmentWithIntersectLine(
            combinedName,
            baseLine.name,
            baseLine.elType,
            intersectLine.name,
            intersectLine.elType,
            throughPoint.name
        );
    }

    protected getSimpleConstructionLabel(): string {
        return 'Tên đường thẳng vuông góc';
    }

    protected getComplexConstructionLabel(): string {
        return 'Tên đường thẳng vuông góc';
    }
}
