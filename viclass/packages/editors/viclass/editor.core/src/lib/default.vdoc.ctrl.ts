import { DocLocalContent, DocumentEditor, MouseEventData, VDoc, VDocCtrl, VDocLayerCtrl, ViewportManager } from './api';
import { SelectHitContext } from './tools';

export abstract class DefaultVDocCtrl implements VDocCtrl {
    layers: VDocLayerCtrl[] = [];

    constructor(
        public state: VDoc,
        public editor: DocumentEditor,
        public viewport: ViewportManager
    ) {}

    addLayer(layer: VDocLayerCtrl) {
        this.layers.push(layer);
        layer.doc = this;
    }

    removeLayer(layer: VDocLayerCtrl) {
        this.layers = this.layers.filter(l => l != layer);
    }

    getLayers(): VDocLayerCtrl[] {
        return this.layers;
    }

    updateByLocalContent(content: DocLocalContent): void {
        throw new Error(`[${this.editor.editorType}] Local content not implemented`);
    }

    abstract checkHit(event: MouseEventData<any>, layer: VDocLayerCtrl): SelectHitContext;
}
