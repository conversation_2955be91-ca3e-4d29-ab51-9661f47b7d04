import { BehaviorSubject } from 'rxjs';
import {
    BaseBoardViewportManager,
    BoardViewportManager,
    BoundaryRectangle,
    browserCursor,
    DefaultMouseEventData,
    DefaultPointerEventData,
    LocatableEvent,
    MouseEventData,
    PointerEventData,
    PointerEventListener,
    PointerHandlingItem,
    pointerTypeDyn,
    pointerTypePenMouse,
    Position,
    ScreenPosition,
    UIPointerEventData,
    VDocLayerCtrl,
    VEventData,
    VEventListener,
    xmlns,
    ZoomEventData,
} from '../api';
import { DefaultEventEmitter } from '../default.event.source';
import { attrsNS } from '../util';
import { BoundaryLayerOptions, SVGElementBoundaryLayer } from './svg.boundary.layer';

const boundSpaceX = 3;
const boundSpaceY = 3;
const resizeHandleSize = 10;

export type BoundaryHandleType =
    | 'boundary'
    | 'resize-0-0'
    | 'resize-0-1'
    | 'resize-0-2'
    | 'resize-1-0'
    | 'resize-1-2'
    | 'resize-2-0'
    | 'resize-2-1'
    | 'resize-2-2';

export type BoundaryChangeEventType = 'boundary-update' | 'boundary-update-start' | 'boundary-update-end';
export interface BoundaryChangeEventData extends VEventData<BoundaryChangeEventType, RectangleObjectBoundary, any> {
    source: RectangleObjectBoundary;
    eventType: BoundaryChangeEventType;
    // TODO: remove mouse event, it is kept so that places still use mouse event can compile
    mouseEvent?: MouseEventData<any>; // can be null when update programmatically
    pointerEvent?: UIPointerEventData<any>; // can be null when update programmatically
    state: {
        startBoundary?: BoundaryRectangle;
        oldBoundary: BoundaryRectangle;
        newBoundary: BoundaryRectangle;
        handle?: BoundaryHandleType;
        selectedEdgeX?: 'start' | 'end'; // which edge is changed in the boundary update in the X axis
        selectedEdgeY?: 'start' | 'end'; // which edge is changed in the boundary update in the Y axis
    };
}

const svgBoundaryLayerCreator = (options: BoundaryLayerOptions): SVGElementBoundaryLayer => {
    if (!options.viewport)
        throw new Error(`Invalid layer creation options when creating ${SVGElementBoundaryLayer.name}`);

    if (!options.nativeMouseEventEmitter && !options.nativePointerEventEmitter) {
        throw new Error(
            `A svg element boundary layer should be created from the boundary instance only. It expects a mouse event emitted to be provided`
        );
    }

    if (!(options.viewport instanceof BaseBoardViewportManager))
        throw new Error(
            `Invalid viewport type. Creating ${SVGElementBoundaryLayer.name} must be invoked by ${BaseBoardViewportManager}.`
        );

    return new SVGElementBoundaryLayer(options);
};

/**
 * This class implements a boundary, including rendering the boundary on an SVGElementBoundaryLayer
 * and handling mouse interaction with the boundary handles.
 *
 * The boundary maintains the order of start and end points this means users can tell if the boundary has changed direction
 */
export class RectangleObjectBoundary {
    layer: SVGElementBoundaryLayer;

    pointerEmitter = new DefaultEventEmitter<UIPointerEventData<VDocLayerCtrl>>();
    mouseEmitter = new DefaultEventEmitter<MouseEventData<VDocLayerCtrl>>();
    boundaryChange = new DefaultEventEmitter<BoundaryChangeEventData>();

    private readonly selectionBorder: SVGRectElement;
    private readonly anchor: SVGRectElement;
    private readonly selectionBorderHandle: SVGRectElement;
    private _isEnablePointerEvent: boolean = false;
    private readonly zoomListener = RectangleObjectBoundary.ZoomListener(this);
    private readonly resizeHandle: SVGRectElement[] = [];
    private _maintainRatio: boolean = false;

    showing: boolean = false;
    handling = false;

    private pointerHandlings: PointerHandlingItem[] = [
        { event: 'pointerdown', button: 0, pointerTypes: pointerTypePenMouse },
        { event: 'pointerup', button: 0, pointerTypes: pointerTypePenMouse },
        { event: 'pointermove', pressedButtons: 1, pointerTypes: pointerTypePenMouse },
        { event: 'pointermove', pressedButtons: 0, pointerTypes: pointerTypePenMouse },

        { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },
        { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
        { event: 'pointermove', pointerTypes: pointerTypeDyn, numTouch: 1 },
    ];

    // because the boundary is attached within the viewport element, which is a svg element
    // needs to calculate the boundary location in the viewport local coordinate system
    // startVL = start viewport local, meaning start position of the boundary in viewport local coordinate
    // startVL is ALWAYS the top left position and is NOT identical to boundary.start
    private startVL!: ScreenPosition;
    // endVL = end viewport local, meaning end position of the boundary in viewport local coordinate
    // endVL is always the bottom right position and is NOT identical to boundary.end
    private endVL!: ScreenPosition;

    private pointerDownBoundaryPos?: ScreenPosition;
    private selectedBoundaryHandle?: BoundaryHandleType;
    private selectedEdgeX?: 'start' | 'end';
    private selectedEdgeY?: 'start' | 'end';

    private readonly gel: Element;

    private backgroundRect?: SVGRectElement;
    private startBoundary?: BoundaryRectangle;
    private blinking: number = 0;
    private XYRatio: number = 1;

    constructor(
        public vm: BoardViewportManager,
        public boundary: BoundaryRectangle
    ) {
        this.mouseEmitter.onBeforeListenerAdded = l => {
            // when someone wants to listen to the mouse events from this
            this.registerMouseEventOnElement(this.selectionBorderHandle, ...this.resizeHandle);
        };

        this.mouseEmitter.onBeforeListenerRemoved = l => {
            this.unregisterMouseEventsOnElement(this.selectionBorderHandle, ...this.resizeHandle);
        };

        this.pointerEmitter.onBeforeListenerAdded = l => {
            // when someone wants to listen to the mouse events from this
            this.registerPointerEventOnElement(this.selectionBorderHandle, ...this.resizeHandle);
        };

        this.pointerEmitter.onBeforeListenerRemoved = l => {
            this.unregisterPointerEventsOnElement(this.selectionBorderHandle, ...this.resizeHandle);
        };

        const options: BoundaryLayerOptions = {
            viewport: this.vm,
            nativeMouseEventEmitter: this.mouseEmitter,
            nativePointerEventEmitter: this.pointerEmitter,
            boundaryCtrl: this,
        };

        vm.addLayerFactory(SVGElementBoundaryLayer, svgBoundaryLayerCreator);

        this.layer = this.vm.requestLayer(SVGElementBoundaryLayer, false, options) as SVGElementBoundaryLayer;

        this.gel = this.layer.renderingRoot;

        this.selectionBorder = document.createElementNS(xmlns, 'rect');
        this.selectionBorderHandle = document.createElementNS(xmlns, 'rect');
        this.anchor = document.createElementNS(xmlns, 'rect');

        this.gel.appendChild(this.anchor);
        Object.assign(this.anchor.style, {
            'pointer-events': 'none',
            visibility: 'hidden',
        });
        Object.assign(this.selectionBorderHandle.style, {
            'pointer-events': 'none',
            visibility: 'hidden',
        });
        this.gel.appendChild(this.selectionBorder);
        this.gel.appendChild(this.selectionBorderHandle);

        for (let i = 0; i < 8; i++) {
            const resizeHandle = document.createElementNS(xmlns, 'rect');

            this.resizeHandle.push(resizeHandle);
            this.gel.appendChild(resizeHandle);
        }
        this.calculateViewportLocal();

        this.fillBoundary(this.boundary);
        this.XYRatio = this.boundary.width! / this.boundary.height!;

        this.layer.attachRenderer(this.render.bind(this));
    }

    private rerenderResizeHandle() {
        this.resizeHandle.forEach((e, i) => {
            if (this._maintainRatio && [1, 3, 4, 6].includes(i)) e.setAttribute('hidden', '');
            else if (e.hasAttribute('hidden')) e.removeAttribute('hidden');
        });
    }

    set maintainRatio(maintainRatio: boolean) {
        this._maintainRatio = maintainRatio;
        this.rerenderResizeHandle();
    }

    private fillBoundary(boundary: BoundaryRectangle) {
        if (!boundary.width) {
            boundary.width = Math.abs(boundary.start!.x - boundary.end!.x);
        }

        if (!boundary.height) {
            boundary.height = Math.abs(boundary.start!.y - boundary.end!.y);
        }
    }

    private static ZoomListener(_p: RectangleObjectBoundary): VEventListener<ZoomEventData> {
        return new (class implements VEventListener<ZoomEventData> {
            onEvent(eventData: ZoomEventData): ZoomEventData {
                //update the size of the handle to ensure their size is zoom independent
                for (const rh of _p.resizeHandle) {
                    attrsNS(rh, {
                        r: resizeHandleSize * eventData.state.zoomLevel,
                    });
                }
                return eventData;
            }
        })();
    }

    blink(interval?: number) {
        if (this.blinking) return;

        this.blinking = setInterval(
            () => {
                if (this.showing) this.hide();
                else this.show();
            },
            interval ? interval : 50
        );
    }

    stopBlinking() {
        if (this.blinking) {
            clearInterval(this.blinking);
            this.blinking = 0;
        }
    }

    show() {
        if (this.showing) return;

        this.vm.addLayer(this.layer);
        if (this._isEnablePointerEvent) {
            this.vm.float(this.layer);
        }
        this.vm.zoomEventEmitter().registerListener(this.zoomListener);

        this.showing = true;
    }

    hide() {
        if (!this.showing) return;

        if (this.backgroundRect) this.removeBackgroundRect();

        this.vm.zoomEventEmitter().unregisterListener(this.zoomListener);
        if (this._isEnablePointerEvent) {
            this.vm.sink(this.layer);
        }
        this.vm.eventManager.clearMouseHandlingOnLayer(this.layer);
        this.vm.removeLayer(this.layer);

        this.showing = false;
    }

    /**
     * Enables mouse event handling for the rectangle boundary when in edit mode.
     *
     * When enabled:
     * - Makes selection borders interactive and visible
     * - Shows resize handles and makes them clickable
     * - Sets up the background rectangle to capture mouse events
     * - Registers resize boundary event listeners with the event manager
     * - Floats the current layer to ensure proper event capturing
     * - Updates the boundary visualization
     *
     * The method only takes effect when the view model is in "EditMode".
     */
    enablePointerEvent() {
        if (this.vm.mode == 'EditMode') {
            this.selectionBorderHandle.setAttribute('style', 'pointer-events:stroke;display:block;fill:none');
            for (const handle of this.resizeHandle) handle.setAttribute('style', 'pointer-events:all;display:block');

            this._isEnablePointerEvent = true;

            if (this.backgroundRect)
                this.backgroundRect.setAttribute(
                    'style',
                    'pointer-events:all;display:block;fill:transparent;touch-action:none'
                );

            // register resize boundary listener
            this.vm.eventManager.registerPointerHandlingOnLayer(
                this.pointerHandlings,
                this.resizeHandleEventListener(),
                this.layer
            );

            if (this.showing) this.vm.float(this.layer); // event manager will listen to events from this layer

            this.onUpdateBoundary();
        }
    }

    disablePointerEvent() {
        if (this._isEnablePointerEvent) {
            this.selectionBorderHandle.setAttribute('style', 'pointer-events:none;display:none');
            for (const handle of this.resizeHandle)
                handle.setAttribute('style', 'pointer-events:none;display:none;touch-action:none');

            this._isEnablePointerEvent = false;

            // this.removeBackgroundRect()

            // clear handler for this layer
            this.vm.eventManager.clearPointerHandlingOnLayer(this.layer);

            if (this.showing) {
                this.vm.sink(this.layer); // event manager will stop listening to events from this layer
            }
            this.onUpdateBoundary();
        }
    }

    private registerMouseEventOnElement(...el: (HTMLElement | SVGElement)[]) {
        for (const h of el) {
            h.addEventListener('contextmenu', this.mouseHandler);
        }
    }

    private registerPointerEventOnElement(...el: (HTMLElement | SVGElement)[]) {
        for (const h of el) {
            h.addEventListener('pointerdown', this.pointerHandler);
            h.addEventListener('pointerup', this.pointerHandler);
            h.addEventListener('pointermove', this.pointerHandler);
        }
    }

    private unregisterMouseEventsOnElement(...el: (HTMLElement | SVGElement)[]) {
        for (const h of el) {
            h.removeEventListener('contextmenu', this.mouseHandler);
        }
    }

    private unregisterPointerEventsOnElement(...el: (HTMLElement | SVGElement)[]) {
        for (const h of el) {
            h.removeEventListener('pointerdown', this.pointerHandler);
            h.removeEventListener('pointerup', this.pointerHandler);
            h.removeEventListener('pointermove', this.pointerHandler);
        }
    }

    mouseHandler = (event: Event) => {
        if (this._isEnablePointerEvent) {
            const data = new DefaultMouseEventData<SVGElementBoundaryLayer>(this.vm, this.layer, event as MouseEvent);
            this.mouseEmitter.emit(data);
        }
    };

    pointerHandler = (event: Event) => {
        if (this._isEnablePointerEvent) {
            const data = new DefaultPointerEventData<SVGElementBoundaryLayer>(
                this.vm,
                this.layer,
                event as PointerEvent
            );

            this.pointerEmitter.emit(data);
        }
    };

    updateBoundary(boundary: BoundaryRectangle) {
        this.boundary = boundary;
        this.fillBoundary(this.boundary);
        this.onUpdateBoundary();
    }

    private calculateViewportLocal() {
        const start: Position = {
            x: Math.min(this.boundary.start!.x, this.boundary.end!.x),
            y: Math.max(this.boundary.start!.y, this.boundary.end!.y),
        };
        const end: Position = {
            x: Math.max(this.boundary.start!.x, this.boundary.end!.x),
            y: Math.min(this.boundary.start!.y, this.boundary.end!.y),
        };

        this.startVL = this.layer.layerPos(start); //this.vm.getScreenPos(start)
        this.endVL = this.layer.layerPos(end); //this.vm.getScreenPos(end)
    }

    private onUpdateBoundary() {
        if (!this.showing) return; // if it is not showing, then do nothing

        this.calculateViewportLocal();

        this.render(this.vm, this.layer);
    }

    private handleTypeFromTarget(target: EventTarget): BoundaryHandleType | undefined {
        if (target === this.selectionBorderHandle) return 'boundary';
        if (target === this.resizeHandle[0]) return 'resize-0-0';
        if (target === this.resizeHandle[1]) return 'resize-0-1';
        if (target === this.resizeHandle[2]) return 'resize-0-2';
        if (target === this.resizeHandle[3]) return 'resize-1-0';
        if (target === this.resizeHandle[4]) return 'resize-1-2';
        if (target === this.resizeHandle[5]) return 'resize-2-0';
        if (target === this.resizeHandle[6]) return 'resize-2-1';
        if (target === this.resizeHandle[7]) return 'resize-2-2';
        return undefined;
    }

    private handlePointerDown(eventData: UIPointerEventData<SVGElementBoundaryLayer>) {
        this.startBoundary = structuredClone(this.boundary);

        const nativeEvent = eventData.nativeEvent;

        delete this.selectedEdgeX;
        delete this.selectedEdgeY;

        // if we selected boundary handle previously, that means the mouse has been released else where
        // on the page, and we didn't receive mouse up, need to redo everything again
        if (nativeEvent.target == this.backgroundRect || this.selectedBoundaryHandle) {
            if (this.backgroundRect) this.removeBackgroundRect(); // remove background rect
            delete this.selectedBoundaryHandle;
            delete this.pointerDownBoundaryPos;
            return;
        }

        this.pointerDownBoundaryPos = this.mouseLocation(eventData);
        this.selectedBoundaryHandle = this.handleTypeFromTarget(nativeEvent.target!);
        this.handling = true;

        const s = this.boundary.start!;
        const e = this.boundary.end!;

        // the selected boundary handle determine which edge is being moved

        switch (this.selectedBoundaryHandle) {
            case 'resize-0-0': {
                // moving top left point
                if (s.x < e.x) this.selectedEdgeX = 'start';
                else this.selectedEdgeX = 'end';
                if (s.y > e.y) this.selectedEdgeY = 'start';
                else this.selectedEdgeY = 'end';
                break;
            }
            case 'resize-0-1': {
                if (s.x < e.x) this.selectedEdgeX = 'start';
                else this.selectedEdgeX = 'end';
                break;
            }
            case 'resize-0-2': {
                if (s.x < e.x) this.selectedEdgeX = 'start';
                else this.selectedEdgeX = 'end';
                if (s.y < e.y) this.selectedEdgeY = 'start';
                else this.selectedEdgeY = 'end';
                break;
            }
            case 'resize-1-0': {
                if (s.y > e.y) this.selectedEdgeY = 'start';
                else this.selectedEdgeY = 'end';
                break;
            }
            case 'resize-1-2': {
                if (s.y < e.y) this.selectedEdgeY = 'start';
                else this.selectedEdgeY = 'end';
                break;
            }
            case 'resize-2-0': {
                if (s.x > e.x) this.selectedEdgeX = 'start';
                else this.selectedEdgeX = 'end';
                if (s.y > e.y) this.selectedEdgeY = 'start';
                else this.selectedEdgeY = 'end';
                break;
            }
            case 'resize-2-1': {
                if (s.x > e.x) this.selectedEdgeX = 'start';
                else this.selectedEdgeX = 'end';
                break;
            }
            case 'resize-2-2': {
                if (s.x > e.x) this.selectedEdgeX = 'start';
                else this.selectedEdgeX = 'end';
                if (s.y < e.y) this.selectedEdgeY = 'start';
                else this.selectedEdgeY = 'end';
                break;
            }
            case 'boundary': // not the resize handle but the boundary handle (move all boundary) then the selected edges is not defined.
                break;
            default: // if doesn't hit any target
                return;
        }

        this.boundaryChange.emit({
            eventType: 'boundary-update-start',
            mouseEvent: eventData,
            pointerEvent: eventData,
            source: this,
            state: {
                startBoundary: this.startBoundary,
                oldBoundary: this.boundary,
                newBoundary: this.boundary,
                handle: this.selectedBoundaryHandle,
                selectedEdgeX: this.selectedEdgeX!,
                selectedEdgeY: this.selectedEdgeY!,
            },
        });

        // when select a handle, we will add in a rect under the mouse, so that the mouse move is smooth
        this.createBackgroundRectForEvents();
    }

    /**
     * Copy the changes in another boundary.
     * If a boundary change and produce an event, we copy the same change
     * to this boundary. This is useful for cases of multiple boundaries are selected, and changes occur in one boundary.
     * @param eventData
     */
    copyChange(eventData: BoundaryChangeEventData): any {
        if (eventData.state?.handle == 'boundary') {
            // this is a boundary movement
            return this.copyMovement(eventData);
        } else {
            return this.copyScale(eventData);
        }
    }

    copyMovement(eventData: BoundaryChangeEventData): any {
        const xMovement = eventData.state.newBoundary.start!.x - eventData.state.oldBoundary.start!.x;
        const yMovement = eventData.state.newBoundary.start!.y - eventData.state.oldBoundary.start!.y;

        const oldBoundary = this.boundary;
        const newBoundary = {
            start: {
                x: this.boundary.start!.x + xMovement,
                y: this.boundary.start!.y + yMovement,
            },
            end: {
                x: this.boundary.end!.x + xMovement,
                y: this.boundary.end!.y + yMovement,
            },
            width: this.boundary.width,
            height: this.boundary.height,
        };

        this.updateBoundary(newBoundary);

        return {
            newBoundary,
            oldBoundary,
        };
    }

    copyScale(eventData: BoundaryChangeEventData): any {
        let scaleX: number;
        let rootX: number;
        let scaleY: number;
        let rootY: number;

        const newBoundary = {
            start: { x: this.boundary.start!.x, y: this.boundary.start!.y },
            end: { x: this.boundary.end!.x, y: this.boundary.end!.y },
            width: this.boundary.width,
            height: this.boundary.height,
        };

        if (eventData.state.selectedEdgeX) {
            // if there is a change in the X direction
            switch (eventData.state.selectedEdgeX) {
                case 'start': {
                    rootX = eventData.state.oldBoundary.end!.x;
                    break;
                }
                case 'end': {
                    rootX = eventData.state.oldBoundary.start!.x;
                    break;
                }
                case undefined:
                    throw new Error('Unable to scale because unable to determine scale root X');
            }
            scaleX = eventData.state.newBoundary.width! / eventData.state.oldBoundary.width!;

            newBoundary.start.x = Math.round((newBoundary.start.x - rootX) * scaleX + rootX);
            newBoundary.end.x = Math.round((newBoundary.end.x - rootX) * scaleX + rootX);
            newBoundary.width = Math.abs(newBoundary.start.x - newBoundary.end.x);
        }

        if (eventData.state.selectedEdgeY) {
            // if there is a change in the X direction
            switch (eventData.state.selectedEdgeY) {
                case 'start': {
                    rootY = eventData.state.oldBoundary.end!.y;
                    break;
                }
                case 'end': {
                    rootY = eventData.state.oldBoundary.start!.y;
                    break;
                }
                case undefined:
                    throw new Error('Unable to scale because unable to determine scale root Y');
            }
            scaleY = eventData.state.newBoundary.height! / eventData.state.oldBoundary.height!;

            newBoundary.start.y = Math.round((newBoundary.start.y - rootY) * scaleY + rootY);
            newBoundary.end.y = Math.round((newBoundary.end.y - rootY) * scaleY + rootY);
            newBoundary.height = Math.abs(newBoundary.start.y - newBoundary.end.y);
        }

        const oldBoundary = this.boundary;
        this.updateBoundary(newBoundary);

        return {
            newBoundary,
            oldBoundary,
        };
    }

    resetBoundary(b: BoundaryRectangle) {
        this.fillBoundary(b); // make sure width height is available
        this.XYRatio = b.width! / b.height!;

        const old = this.boundary;
        this.updateBoundary(b);
        this.boundaryChange.emit({
            eventType: 'boundary-update',
            source: this,
            state: {
                oldBoundary: old,
                newBoundary: this.boundary,
            },
        });
    }

    updateBoundaryEndWithEvent(b: BoundaryRectangle) {
        const old = this.boundary;
        this.updateBoundary(b);
        // external update programatically, so no start boundary
        this.boundaryChange.emit({
            eventType: 'boundary-update-end',
            source: this,
            state: {
                oldBoundary: old,
                newBoundary: this.boundary,
            },
        });
    }

    private createBackgroundRectForEvents() {
        this.removeBackgroundRect();
        this.backgroundRect = document.createElementNS(xmlns, 'rect');

        Object.assign(this.backgroundRect.style, {
            'pointer-events': 'all',
            touchAction: 'none',
            fill: 'transparent',
        });

        this.gel.appendChild(this.backgroundRect);
        this.registerPointerEventOnElement(this.backgroundRect);

        // set the rect to cover the board
        const b = this.vm.boardBoundary;

        const tl = this.layer.layerPos(b.start);
        const br = this.layer.layerPos(b.end);

        this.backgroundRect.setAttributeNS(null, 'x', `${tl.x}`);
        this.backgroundRect.setAttributeNS(null, 'y', `${tl.y}`);
        this.backgroundRect.setAttributeNS(null, 'width', `${Math.abs(br.x - tl.x)}`);
        this.backgroundRect.setAttributeNS(null, 'height', `${Math.abs(br.y - tl.y)}`);

        // refloating the layer
        this.vm.float(this.layer);
    }

    private removeBackgroundRect() {
        if (this.backgroundRect) {
            this.unregisterPointerEventsOnElement(this.backgroundRect);
            this.backgroundRect.remove();
            delete this.backgroundRect;
        }
    }

    mouseLocation(eventData: LocatableEvent<SVGElementBoundaryLayer>) {
        const vm = this.vm as BoardViewportManager;
        const event: MouseEvent = eventData.nativeEvent;
        const cX = event.clientX;
        const cY = event.clientY;
        const bR = this.anchor.getBoundingClientRect();

        const rB = this.renderingRootRectInBoard();

        return {
            x: (cX - bR.x) * vm.zoomLevel + rB.x, // the assumption is this layer is attached to the view port
            y: -(cY - bR.y) * vm.zoomLevel + rB.y, // the assumption is this layer is attached to the view port
        };
    }

    private handlePointerUp(eventData: UIPointerEventData<SVGElementBoundaryLayer>) {
        this.handling = false;

        if (this.startBoundary && this.selectedBoundaryHandle) {
            const boundaryEvent: BoundaryChangeEventData = {
                eventType: 'boundary-update-end',
                mouseEvent: eventData,
                pointerEvent: eventData,
                source: this,
                state: {
                    startBoundary: this.startBoundary,
                    oldBoundary: this.boundary,
                    newBoundary: this.boundary,
                    handle: this.selectedBoundaryHandle,
                    selectedEdgeX: this.selectedEdgeX,
                    selectedEdgeY: this.selectedEdgeY,
                },
            };
            this.boundaryChange.emit(boundaryEvent);
        }

        this.removeBackgroundRect();

        delete this.pointerDownBoundaryPos;
        delete this.selectedBoundaryHandle;
    }

    private handleDiagonalResize(
        pointerDownPosition: ScreenPosition,
        startPosition: Position,
        endPosition: Position
    ): { newStartY: number; newEndY: number } {
        // Early return if boundary options or XY ratio are not defined
        if (this.XYRatio === undefined) throw new Error('XYRation should not be undefined');

        const XYRatio = this.XYRatio;
        // Calculate deltas based on selected edges
        let deltaStartX: number = 0;
        let deltaEndX: number = 0;

        const isStartStartOrEndEnd =
            (this.selectedEdgeX === 'start' && this.selectedEdgeY === 'start') ||
            (this.selectedEdgeX === 'end' && this.selectedEdgeY === 'end');
        const isEndStartOrStartEnd =
            (this.selectedEdgeX === 'end' && this.selectedEdgeY === 'start') ||
            (this.selectedEdgeX === 'start' && this.selectedEdgeY === 'end');

        if (isStartStartOrEndEnd) {
            deltaStartX = pointerDownPosition.x - endPosition.x;
            deltaEndX = pointerDownPosition.x - startPosition.x;
        } else if (isEndStartOrStartEnd) {
            deltaStartX = pointerDownPosition.x - startPosition.x;
            deltaEndX = pointerDownPosition.x - endPosition.x;
        }

        // Compute Y deltas using XY ratio
        let deltaStartY = Math.abs(deltaStartX / XYRatio);
        let deltaEndY = Math.abs(deltaEndX / XYRatio);

        // Adjust Y deltas based on selected edges
        const invertY =
            (this.selectedEdgeX === 'start' && this.selectedEdgeY === 'end') ||
            (this.selectedEdgeX === 'end' && this.selectedEdgeY === 'end');

        if (invertY) {
            deltaStartY = -deltaStartY;
            deltaEndY = -deltaEndY;
        }

        // Calculate new Y positions
        const newStartY = endPosition.y + deltaStartY;
        const newEndY = startPosition.y + deltaEndY;

        return {
            newStartY,
            newEndY,
        };
    }

    private handlePointerMove(eventData: UIPointerEventData<SVGElementBoundaryLayer>) {
        if (!this.pointerDownBoundaryPos) return;

        const zoom = this.vm.zoomLevel;

        const startPos: Position = {
            x: this.boundary.start!.x,
            y: this.boundary.start!.y,
        };
        const endPos: Position = {
            x: this.boundary.end!.x,
            y: this.boundary.end!.y,
        };

        const previousPos = this.pointerDownBoundaryPos;
        this.pointerDownBoundaryPos = this.mouseLocation(eventData);

        const deltaScreenX = this.pointerDownBoundaryPos.x - previousPos.x;
        const deltaScreenY = this.pointerDownBoundaryPos.y - previousPos.y;
        let dirX = 0;
        let dirY = 0;

        if (endPos.x > startPos.x) dirX = -1;
        else dirX = 1;

        if (endPos.y < startPos.y) dirY = -1;
        else dirX = 1;

        let newPosStartY = this.pointerDownBoundaryPos.y;
        let newPosEndY = this.pointerDownBoundaryPos.y;

        if (this._maintainRatio) {
            const newPos = this.handleDiagonalResize(this.pointerDownBoundaryPos, startPos, endPos);
            newPosStartY = newPos.newStartY;
            newPosEndY = newPos.newEndY;
        }

        switch (this.selectedBoundaryHandle) {
            case 'boundary': // for boundary handle, it is a translation of both start and end
                startPos.x += Math.round(deltaScreenX);
                startPos.y += Math.round(deltaScreenY);
                endPos.x += Math.round(deltaScreenX);
                endPos.y += Math.round(deltaScreenY);
                break;
            case undefined:
                throw new Error(
                    'Selected boundary is none when handling mouse move. This means the selected boundary is not correctly set in mouse down'
                );
            default: // otherwise, the selected edge will be changed
                if (this.selectedEdgeX == 'start')
                    startPos.x = Math.round(this.pointerDownBoundaryPos.x - dirX * boundSpaceX * zoom);
                else if (this.selectedEdgeX == 'end')
                    endPos.x = Math.round(this.pointerDownBoundaryPos.x + dirX * boundSpaceX * zoom);

                if (this.selectedEdgeY == 'start') startPos.y = Math.round(newPosStartY + dirY * boundSpaceY * zoom);
                else if (this.selectedEdgeY == 'end') endPos.y = Math.round(newPosEndY - dirY * boundSpaceY * zoom);
                break;
        }

        const oldBoundary = this.boundary;
        this.updateBoundary({ start: startPos, end: endPos });

        // when the boundary is changed because of user mouse movement, we inform
        // listeners
        const boundaryEvent: BoundaryChangeEventData = {
            source: this,
            eventType: 'boundary-update',
            mouseEvent: eventData,
            pointerEvent: eventData,
            state: {
                startBoundary: this.startBoundary,
                oldBoundary: oldBoundary,
                newBoundary: this.boundary,
                handle: this.selectedBoundaryHandle,
                selectedEdgeX: this.selectedEdgeX,
                selectedEdgeY: this.selectedEdgeY,
            },
        };

        this.boundaryChange.emit(boundaryEvent);
    }

    /**
     * @returns The listener that will listen to the events from event manager and change the size of the boundary and
     * re-render the boundary on the layer. Internal update will cause boundary change events to emit
     */
    private resizeHandleEventListener(): PointerEventListener<SVGElementBoundaryLayer> {
        const _p = this;
        let requestedFrame = false;
        let latestPointerMove: UIPointerEventData<any>;

        return new (class implements PointerEventListener<SVGElementBoundaryLayer> {
            cursor = new BehaviorSubject(browserCursor);

            onEvent(eventData: PointerEventData<SVGElementBoundaryLayer>): PointerEventData<SVGElementBoundaryLayer> {
                if (!('nativeEvent' in eventData)) return eventData;

                const nativeEvent = eventData.nativeEvent;
                switch (nativeEvent.type) {
                    case 'pointerdown':
                        _p.handlePointerDown(eventData);
                        break;
                    case 'pointerup':
                        _p.handlePointerUp(eventData);
                        break;
                    case 'pointermove':
                        latestPointerMove = eventData; // record the latest mouse move to be handled, because there can be several mouse move occurred before repaint
                        if (!requestedFrame) {
                            requestAnimationFrame(() => {
                                requestedFrame = false;
                                _p.handlePointerMove(latestPointerMove);
                            });
                            requestedFrame = true;
                        }
                        break;
                }

                eventData.continue = false;
                return eventData;
            }
        })();
    }

    get nativeEl(): Element {
        return this.gel;
    }

    get w(): number {
        return Math.abs(this.startVL.x - this.endVL.x);
    }

    get h(): number {
        return Math.abs(this.startVL.y - this.endVL.y);
    }

    private render(viewport: BoardViewportManager, layer: SVGElementBoundaryLayer) {
        const zoom = viewport.zoomLevel;
        attrsNS(this.anchor, {
            x: this.startVL.x,
            y: this.startVL.y,
            width: 1,
            height: 1,
        });

        attrsNS(this.selectionBorder, {
            x: this.startVL.x - boundSpaceX,
            y: this.startVL.y - boundSpaceY,
            width: this.w + boundSpaceX * 2,
            height: this.h + boundSpaceY * 2,
            class: 'vi-b-border',
        });

        attrsNS(this.selectionBorderHandle, {
            x: this.startVL.x - boundSpaceX,
            y: this.startVL.y - boundSpaceY,
            width: this.w + boundSpaceX * 2,
            height: this.h + boundSpaceY * 2,
            class: 'vi-b-handle',
        });

        if (this._isEnablePointerEvent) {
            const hx = this.startVL.x + this.w / 2;
            const hy = this.startVL.y + this.h / 2;
            let index = -1;
            for (let i = -1; i <= 1; i++)
                for (let j = -1; j <= 1; j++) {
                    if (i == 0 && j == 0) continue;
                    index++;
                    const handleClass = 'h-' + (i + 1) + '-' + (j + 1);
                    attrsNS(this.resizeHandle[index], {
                        x:
                            hx +
                            i * (this.w / 2 + boundSpaceX + (resizeHandleSize / 2) * zoom) -
                            (resizeHandleSize * zoom) / 2,
                        y:
                            hy +
                            j * (this.h / 2 + boundSpaceY + (resizeHandleSize / 2) * zoom) -
                            (resizeHandleSize * zoom) / 2,
                        width: resizeHandleSize * zoom, // make the size zoom independent
                        height: resizeHandleSize * zoom,
                        class: 'vi-b-resize-handle ' + handleClass,
                    });
                }
        }
    }

    // because the elements are added, the rectangle boundary of the rendering root is not simply the boundary
    renderingRootRectInBoard(): Position {
        const start: Position = {
            x: Math.min(this.boundary.start!.x, this.boundary.end!.x),
            y: Math.max(this.boundary.start!.y, this.boundary.end!.y),
        };

        return start;
    }

    destroy() {
        this.removeBackgroundRect();
        this.hide();
        this.stopBlinking();
        this.boundaryChange.clearListeners();
        this.mouseEmitter.clearListeners();
    }
}
