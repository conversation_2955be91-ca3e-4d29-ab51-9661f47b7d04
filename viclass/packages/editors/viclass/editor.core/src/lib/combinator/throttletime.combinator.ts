import { Subject, Subscription } from 'rxjs';
import { sampleTime, tap } from 'rxjs/operators';
import { Cmd, CmdCombinator } from '../api';

export class ThrottleCombinator implements CmdCombinator {
    private sender!: (cmd: Cmd<any>) => void;
    private src!: Subject<Cmd<any>>;
    private subscription!: Subscription;

    private lastCmd: Cmd<any> | null = null;

    constructor(private period: number = 50) {}

    /**
     * Initializes the command stream and sets up throttling behavior.
     */
    startSequence(sender: (cmd: Cmd<any>) => void) {
        this.sender = sender;
        this.src = new Subject<Cmd<any>>();

        // Sample incoming commands every `period` ms and send them
        this.subscription = this.src
            .pipe(
                sampleTime(this.period),
                tap(cmd => {
                    // Reset lastCmd since it was just sent
                    this.lastCmd = null;
                    this.sender(cmd);
                })
            )
            .subscribe();
    }

    isCombining: boolean = false;

    /**
     * Push a new command into the stream and remember it as the latest.
     */
    combine(cmd: Cmd<any>) {
        this.lastCmd = cmd;
        this.src.next(cmd);
    }

    /**
     * Clean up and ensure the last command (if not already sent) is flushed.
     */
    async endSequenceAndFlush(): Promise<void> {
        this.src.complete(); // Complete the input stream

        // If the last command wasn’t picked up by sampleTime, send it manually
        if (this.lastCmd) {
            this.sender(this.lastCmd);
        }

        // Clean up the subscription
        this.subscription.unsubscribe();
    }
}
