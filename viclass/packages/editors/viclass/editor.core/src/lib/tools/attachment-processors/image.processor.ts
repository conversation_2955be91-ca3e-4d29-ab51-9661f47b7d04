import imageCompression, { Options } from 'browser-image-compression';
import { AttachmentProcessor } from './attachment.processor';

type CompressOptions = Options;

const MB = 1024 * 1024;

export type ImageProcessOptions = {
    /** @default 1048576 (1MB) */
    maxSize?: number;
    /**
     * compressedFile will scale down by ratio to a point that width or height is smaller than maxWidthOrHeight
     * @default 3000
     */
    maxWidthOrHeight?: number;
    /** A function takes one progress argument (progress from 0 to 100) */
    onProgress?: (progress: number) => void;
};

export class ImageProcessor extends AttachmentProcessor {
    supportFileType(fileType: string): boolean {
        return fileType.startsWith('image/');
    }

    async processFile(file: File | Blob, options: ImageProcessOptions = {}): Promise<Blob> {
        // cast type as there is mismatch between the definition and the implementation of the library
        const { maxSize, ...otherOptions } = options;

        const chosenMaxSize = maxSize || MB; // provided size or 1MB

        return this.compressLoop(file as File, {
            maxSizeMB: chosenMaxSize / MB,
            maxWidthOrHeight: 3000, // 3000px
            initialQuality: 1, // full quality before scale down
            useWebWorker: true,
            ...otherOptions,
        });
    }

    /**
     * Recursively compress the image until it is smaller than predefined max size
     * @returns
     */
    async compressLoop(file: File | Blob, options: CompressOptions, depth = 0): Promise<Blob> {
        if (depth > 5) return file as Blob;

        const resultBlob: Blob = (await imageCompression(file as File, options)) as Blob;

        const maxSizeBytes = (options.maxSizeMB || 1) * MB;
        if (resultBlob.size > maxSizeBytes) {
            // calculate new options
            const degradeRatio = Math.max(0.1, Math.min(0.8, maxSizeBytes / resultBlob.size));
            const newOptions: CompressOptions = {
                ...options,
                initialQuality: degradeRatio,
                maxWidthOrHeight: (options.maxWidthOrHeight || 3000) * degradeRatio,
            };

            return this.compressLoop(resultBlob, newOptions, depth + 1);
        }

        return resultBlob;
    }
}
