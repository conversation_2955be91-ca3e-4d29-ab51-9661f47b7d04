import { BehaviorSubject } from 'rxjs';
import {
    BaseBoardViewportManager,
    Cursor, // Added
    NativeEventTarget,
    newCursor,
    PointerEventData, // Changed from MouseEventData
    PointerEventListener,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerType<PERSON>en,
    ScreenPosition,
    UIPointerEventData,
} from '../api';
import { isCtrlOrMeta } from '../util';
import { CommonTool, CommonToolType } from './common.tool';
import { FEATURE_PAN, PanFeature } from './pan.zoom.feature';
import { SelectTool } from './select.tool';

type PointerHandler = PointerEventListener<NativeEventTarget<any>> & {
    onFocus();
    onBlur();
};

// if we use Ctrl + Pointer MOVE to pan,
// we only start panning once the pointer has moved
// in either direction passing this threshold
export const START_PAN_THRESHOLD = 10;

/**
 *
 * <AUTHOR>
 */
export class PanTool extends CommonTool {
    override readonly toolType: CommonToolType = 'pan';

    /**
     * Pointer event handler for the pan tool that manages panning interactions.
     * Implements PointerEventListener interface to handle pointer events for panning the viewport.
     */
    override readonly pointerHandler: PointerHandler = new (class
        implements PointerEventListener<NativeEventTarget<any>>
    {
        /** Subject that tracks and emits the current cursor state */
        cursor = new BehaviorSubject<Cursor[] | undefined>(undefined);

        /** Array containing the hand cursor icon used when panning is active */
        private readonly cursorHand = [newCursor('cursor_hand')];
        private delayPanStartPos?: ScreenPosition;

        constructor(private p: PanTool) {}

        /**
         * Initiates the panning operation
         * @param event The pointer event data that triggered panning
         */
        private startPanning(event: UIPointerEventData<NativeEventTarget<any>>) {
            // Changed parameter type
            this.p.moveStarted = true;
            if (this.p.selectTool && this.p.selectTool.curDocSelection.length === 1) {
                const docCtrl = this.p.selectTool.curDocSelection[0].doc;
                if (docCtrl.editor.isSupportFeature(FEATURE_PAN)) {
                    const feature = this.p.panFeature?.getPanHandler(docCtrl.viewport.id, docCtrl.editor.editorType);
                    // Pass the original UIPointerEventData if needed, or adapt isPanHandleAble
                    // For now, assuming it might need the base event or specific properties
                    if (feature?.isPanHandleAble(docCtrl, event as any)) feature?.startPan(docCtrl); // Cast might be needed depending on isPanHandleAble signature
                }
            }
        }

        /**
         * Stops the panning operation and cleans up
         */
        private stopPanning() {
            this.p.moveStarted = false;
            this.delayPanStartPos = undefined;
            if (this.p.selectTool && this.p.selectTool.curDocSelection.length === 1) {
                const docCtrl = this.p.selectTool.curDocSelection[0].doc;
                if (docCtrl.editor.isSupportFeature(FEATURE_PAN)) {
                    const feature = this.p.panFeature?.getPanHandler(docCtrl.viewport.id, docCtrl.editor.editorType);
                    if (feature?.isPanHandling(docCtrl)) feature?.stopPan(docCtrl);
                }
            }
        }

        private selfFocus() {
            if (this.p.toolbar.curTool !== 'pan') {
                this.p.selfFocus = true; // Track temporary tool activation
                this.p.toolbar.focus('pan', true); // Switch active tool to pan
            }
        }

        private clearSelfFocus() {
            if (this.p.selfFocus) {
                this.p.selfFocus = false; // Clear self-focus flag
                this.p.toolbar.blur('pan', true); // Remove focus from pan tool
            }
        }

        onFocus() {
            if (this.cursor.value !== this.cursorHand) this.cursor.next(this.cursorHand);
        }

        onBlur() {
            if (this.cursor.value === this.cursorHand) this.cursor.next(undefined);
        }

        /**
         * Performs the actual panning operation by translating the viewport
         * @param event The pointer event containing movement data
         * @returns boolean indicating if panning was performed
         */
        private pan(event: UIPointerEventData<NativeEventTarget<any>>) {
            // Changed parameter type
            if (this.p.selectTool && this.p.selectTool.curDocSelection.length == 1) {
                const docCtrl = this.p.selectTool.curDocSelection[0].doc;
                if (docCtrl.editor.isSupportFeature(FEATURE_PAN)) {
                    const feature = this.p.panFeature?.getPanHandler(docCtrl.viewport.id, docCtrl.editor.editorType);
                    if (feature?.isPanHandling(docCtrl)) {
                        // Use PointerEvent properties
                        feature.translateViewpoint(docCtrl, -event.nativeEvent.movementX, -event.nativeEvent.movementY);
                        return true;
                    }
                }
            }

            if (event.viewport && event.viewport instanceof BaseBoardViewportManager) {
                // Use PointerEvent properties
                event.viewport.translateViewpoint(
                    -event.nativeEvent.movementX,
                    -event.nativeEvent.movementY,
                    event.nativeEvent
                );
                return true;
            }

            return false;
        }

        /**
         * Main event handler that processes pointer events for panning functionality
         * Handles the following interactions:
         * - Pointer movement when pan tool is actively selected
         * - Pan activation via middle pointer button press (button 1, buttons 4)
         * - Pan activation with Ctrl+primary pointer button combination (button 0, buttons 1)
         * - Cursor state changes during different pan states
         * - Starting, maintaining, and stopping pan operations
         *
         * @param event The pointer event data to process
         * @returns The potentially modified event data, with continue flag set if event was handled
         */
        onEvent(
            event: PointerEventData<NativeEventTarget<any>> // Changed parameter type
        ): PointerEventData<NativeEventTarget<any>> | Promise<PointerEventData<NativeEventTarget<any>>> {
            if (this.p.toolbar.isDisabled() || this.p.toolbar.isToolDisable('pan')) return event;
            let handled = false;

            // Handle inferred event
            if (event.eventType == 'longpress') {
                const uiEvent = event.pointerDowns!.values()[0];
                //if long press, and not start panning yet, then we start, otherwise, just ignore
                if (!this.p.moveStarted) {
                    this.selfFocus(); // start panning internally
                    this.startPanning(uiEvent);
                    handled = true;
                }
            } else if (event.eventType == 'numdpointer') {
                //if numdpointer, it means there is a change in touch / pen, then we cancel all in progress panning
                if (event.totalTouch != 1) {
                    this.delayPanStartPos = undefined;
                    this.clearSelfFocus();
                    this.stopPanning();
                    handled = true;
                }
            } else {
                // if event is actual UI event
                const uiEvent = event as UIPointerEventData<NativeEventTarget<any>>; // Cast for easier access

                if (this.p.toolbar.isDisabled() || this.p.toolbar.isToolDisable(this.p.toolType)) return uiEvent;

                const isPointerMove = uiEvent.eventType === 'pointermove';
                const isPointerDown = uiEvent.eventType === 'pointerdown';
                const isPointerUp = uiEvent.eventType === 'pointerup';

                // PointerEvent.button: 0=main (left), 1=aux (middle), 2=secondary (right)
                // PointerEvent.buttons: Bitmask: 1=main, 2=secondary, 4=aux

                // If touch or middle button is hold, then we activate the pan tool
                const isHoldMiddleButtonOrTouch =
                    uiEvent.nativeEvent.buttons === 4 || uiEvent.nativeEvent.pointerType === 'touch';
                const isHoldPrimaryButton = uiEvent.nativeEvent.buttons === 1;
                const isHoldSupportKeys = isCtrlOrMeta(uiEvent.nativeEvent);
                const isNotTouch = uiEvent.nativeEvent.pointerType !== 'touch';
                const isPen = uiEvent.nativeEvent.pointerType === 'pen';
                const isMouse = uiEvent.nativeEvent.pointerType == 'mouse';

                if (isPointerDown) {
                    // if this is a middle button and we have not focused, then focus to start panning
                    if (isHoldMiddleButtonOrTouch) {
                        if (this.p.toolbar.curTool != 'pan') this.selfFocus();
                        if (!this.p.moveStarted) this.startPanning(uiEvent);
                        handled = true;
                    } else if ((isHoldSupportKeys && isHoldPrimaryButton) || isPen) {
                        // we don't immediately focus and start panning if ctrl + pointer down because it will block select tool from
                        // doing its work, we only start focusing pan tool if we have moved the mouse certain distance
                        this.delayPanStartPos = {
                            x: uiEvent.nativeEvent.clientX,
                            y: uiEvent.nativeEvent.clientY,
                        };
                    } else if (isHoldPrimaryButton && this.p.toolbar.curTool == 'pan' && !this.p.moveStarted) {
                        this.startPanning(uiEvent);
                        handled = true;
                    }
                } else if (isPointerMove) {
                    // Check if primary or middle button is held down
                    if (isHoldPrimaryButton || isHoldMiddleButtonOrTouch || (isPen && this.delayPanStartPos)) {
                        // if not hold ctrl key, and movement is of a mouse, and middle button is not down
                        if (
                            isNotTouch &&
                            !isPen && // if on desktop
                            ((!isHoldSupportKeys && isHoldPrimaryButton && this.p.selfFocus) || !isHoldPrimaryButton) && // and not holding support key or primary button
                            !isHoldMiddleButtonOrTouch &&
                            this.p.moveStarted
                        ) {
                            this.stopPanning();
                            this.clearSelfFocus();
                            handled = true;
                        } else {
                            // if we are delaying pan because of using ctrl + pointer down, we start panning and focus
                            // when the pointer has moved certain distance
                            if (this.delayPanStartPos && !this.p.moveStarted) {
                                const dx = uiEvent.nativeEvent.clientX - this.delayPanStartPos.x;
                                const dy = uiEvent.nativeEvent.clientY - this.delayPanStartPos.y;
                                if (Math.abs(dx) > START_PAN_THRESHOLD || Math.abs(dy) > START_PAN_THRESHOLD) {
                                    this.delayPanStartPos = undefined; // clear it
                                    if (this.p.toolbar.curTool != 'pan') this.selfFocus();
                                    this.startPanning(uiEvent);
                                }
                            } else {
                                if (!this.p.moveStarted && !isPen) {
                                    // if the movement is from a pen, we requires a pointer down first
                                    // If movement is from a mouse we don't immediately focus and start panning
                                    // if ctrl + pointer down because it will block select tool from
                                    // doing its work, we only start focusing pan tool if we have moved the mouse certain distance
                                    this.delayPanStartPos = {
                                        x: uiEvent.nativeEvent.clientX,
                                        y: uiEvent.nativeEvent.clientY,
                                    };
                                }
                            }
                            if (this.p.moveStarted && this.pan(uiEvent)) handled = true; // Execute pan movement if operation is active
                        }
                    } else if (this.p.moveStarted) {
                        // if move is started and mouse is not pressed in middle or main button and also is not move on touch
                        this.stopPanning();
                        this.clearSelfFocus();
                        handled = true;
                    }
                } else if (isPointerUp) {
                    console.log('STOPPING PAN');
                    if ((isMouse || isPen) && this.p.moveStarted) {
                        console.log('STOPPING PEN PAN');
                        this.stopPanning();
                        this.delayPanStartPos = undefined;
                        this.clearSelfFocus();
                        handled = true;
                    }
                }
            }

            // Prevent further processing if event was handled
            if (handled) {
                event.continue = false;
            }

            return event;
        }
    })(this);

    // Keep touch handling commented out as it wasn't requested to be changed
    // override readonly touchHandler: TouchEventListener<NativeEventTarget<any>> = ...

    private moveStarted = false;
    private selfFocus = false;

    constructor(
        local: boolean,
        global: boolean,
        private selectTool?: SelectTool,
        private panFeature?: PanFeature
    ) {
        super();

        // Clear old mouseHandling and define pointerHandling
        this.mouseHandling.length = 0; // Clear the array
        this.pointerHandling.length = 0; // Ensure pointerHandling is also clear initially

        if (local) {
            this.pointerHandling.push(
                // Primary button down/up/move when tool is active
                { event: 'pointerdown', button: 0, pointerTypes: pointerTypeMouse },

                { event: 'pointerup', button: 0, pointerTypes: pointerTypeMouse },

                { event: 'pointermove', pressedButtons: 1, pointerTypes: pointerTypeMouse }, // Move while primary is down

                // Middle button down/up/move when tool is active
                { event: 'pointerdown', button: 1, pointerTypes: pointerTypeMouse },
                { event: 'pointerup', button: 1, pointerTypes: pointerTypeMouse },
                { event: 'pointermove', pressedButtons: 4, pointerTypes: pointerTypeMouse }, // Move while middle is down

                // Ctrl + Primary button down/up/move when tool is active
                { event: 'pointerdown', button: 0, keys: ['ctrl'], pointerTypes: pointerTypeMouse },
                { event: 'pointerup', button: 0, keys: ['ctrl'], pointerTypes: pointerTypeMouse },
                { event: 'pointermove', pressedButtons: 1, keys: ['ctrl'], pointerTypes: pointerTypeMouse }, // Move while ctrl+primary is down

                { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 1 },
                { event: 'pointermove', pointerTypes: pointerTypeDyn, numTouch: 1 },
                { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

                { event: 'pointermove', pointerTypes: pointerTypePen, numPointer: 1 }
            );
        }

        if (global || local) {
            this.pointerHandling.push(
                // IMPORTANT: for global event, we just need to register for trigger event
                // once these event hit, the tool should be focused transiently so that it can
                // listen for subsequent event

                // Middle button down/up/move globally (for activating pan temporarily)
                { event: 'pointerdown', button: 1, pointerTypes: pointerTypeMouse, global: true },

                // Ctrl + Primary button down/up/move globally (for activating pan temporarily)
                { event: 'pointerdown', button: 0, keys: ['ctrl'], pointerTypes: pointerTypeMouse, global: true },
                // because when ctrl + primary pan, we delay a bit, that's why we have to listen globally
                // so that pointer move is given to the pan tool to detect the delay
                {
                    event: 'pointermove',
                    pressedButtons: 1,
                    keys: ['ctrl'],
                    pointerTypes: pointerTypeMouse,
                    global: true,
                },

                { event: 'longpress', pointerTypes: pointerTypeDyn, numTouch: 1, global: true },
                { event: 'pointermove', pointerTypes: pointerTypeDyn, numTouch: 1, global: true },

                // handling for pen
                { event: 'pointermove', pointerTypes: pointerTypePen, numPointer: 1, global: true },
                { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1, global: true }
            );

            // Keep commented-out touch handling
            // this.touchHandling.push(...)
        }
    }

    override onFocus() {
        this.pointerHandler.onFocus();
    }

    override onBlur() {
        this.pointerHandler.onBlur();
    }
}
