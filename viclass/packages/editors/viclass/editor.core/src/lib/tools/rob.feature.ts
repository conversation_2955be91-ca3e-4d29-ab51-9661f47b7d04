import {
    BaseBoardViewportManager,
    BoundaryRectangle,
    CoordinatorEvent,
    EditorAddedCES,
    EditorCoordinator,
    VEventListener,
    ViewportDisableCES,
    ViewportRemovedCES,
} from '../api';
import { RectangleObjectBoundary } from '../boundary';

export const FEATURE_ROB = 'common_rob_feature';

/**
 * RectangleObjectBoundary Feature, use to handle RectangleObjectBoundary
 *
 * <AUTHOR>
 */
export class ROBFeature {
    private readonly _robsByVP: Map<string, RectangleObjectBoundary[]> = new Map();

    constructor(private _coordinator: EditorCoordinator) {
        this._coordinator.registerCoordEventListener(new this._COORD_EVENT_HANDLER(this));
    }

    requestROB(vpId: string, boundary: BoundaryRectangle): RectangleObjectBoundary {
        const vm = this._coordinator.getViewportManager(vpId) as BaseBoardViewportManager;
        const rob = new RectangleObjectBoundary(vm, boundary);
        let robs = this._robsByVP.get(vpId);
        if (!robs) {
            robs = [];
            this._robsByVP.set(vpId, robs);
        }
        robs.push(rob);
        return rob;
    }

    releaseROB(vpId: string, rob: RectangleObjectBoundary) {
        const robs = this._robsByVP.get(vpId);
        if (!robs) return;
        const idx = robs.indexOf(rob);
        if (idx >= 0) {
            const rob = robs.splice(idx, 1)[0];
            rob.destroy();
        }
    }

    private _COORD_EVENT_HANDLER = class implements VEventListener<CoordinatorEvent> {
        constructor(private p: ROBFeature) {}

        onEvent(eventData: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
            switch (eventData.eventType) {
                case 'viewport-added': {
                    break;
                }
                case 'viewport-removed': {
                    const eventState = eventData.state as ViewportRemovedCES;
                    this.p._robsByVP.get(eventState.vmId)?.forEach(b => b.destroy());
                    this.p._robsByVP.delete(eventState.vmId);
                    break;
                }
                case 'viewport-disabled': {
                    const eventState = eventData.state as ViewportDisableCES;
                    this.p._robsByVP.get(eventState.vmId)?.forEach(b => b.disablePointerEvent());
                    break;
                }
                case 'viewport-edit-mode': {
                    const eventState = eventData.state!;
                    this.p._robsByVP.get(eventState.vmId)?.forEach(b => b.enablePointerEvent());
                    break;
                }
                case 'viewport-interactive-mode':
                case 'viewport-view-mode': {
                    const eventState = eventData.state!;
                    this.p._robsByVP.get(eventState.vmId)?.forEach(b => b.disablePointerEvent());
                    break;
                }
                case 'editor-added': {
                    const ed = (eventData.state as EditorAddedCES).editor;
                    if (ed.isSupportFeature(FEATURE_ROB)) {
                        ed.onFeatureInitialization(FEATURE_ROB, this.p);
                    }
                    break;
                }
                default:
                    break;
            }

            return eventData;
        }
    };
}
