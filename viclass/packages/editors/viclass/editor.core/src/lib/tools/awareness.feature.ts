/**
 * Awareness Feature Module
 *
 * This module provides a feature for managing awareness across multiple viewports.
 * It handles the registration, coordination, and lifecycle of awareness tools,
 * and provides utility functions for creating different types of awareness.
 */

import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { CoordinatorEvent, EditorAddedCES, EditorCoordinator, VDocCtrl, VEventListener, ViewportId } from '../api';
import { randomId } from '../util';
import { Awareness, AwarenessCmdOption, AwarenessTool } from './awareness.tool';

/**
 * Feature identifier for the awareness feature
 * Used to register this feature with editors
 */
export const FEATURE_AWARENESS = 'common_awareness_feature';

/**
 * Generates a random unique ID for awareness instances
 * @returns A string ID prefixed with 'awareness-'
 */
export const initRandomAwarenessId = () => randomId('awareness-');

/**
 * Creates an awareness command option for document-related events
 * Used to show what a user is doing with a specific document element
 *
 * @param id - Unique identifier for this awareness
 * @param docCtrl - Document controller that this awareness relates to
 * @returns An awareness command option configured for document events
 */
export const buildDocumentAwarenessCmdOption = (id: string, docCtrl: VDocCtrl) =>
    <AwarenessCmdOption>{
        type: 'aw-document',
        id,
        docInfo: {
            globalId: docCtrl.state.globalId,
            localId: docCtrl.state.id,
        },
        useScheduler: true,
        startAfterSeconds: 1,
        expireAfterSeconds: 5,
    };

/**
 * Creates an awareness command option for one-shot events
 * Used for transient events like mouse movements that don't need persistence
 *
 * @param id - Unique identifier for this awareness
 * @param payload - Optional additional data to include
 * @returns An awareness command option configured for one-shot events
 */
export const buildOneshotAwarenessCmdOption = (id: string, payload?: any) =>
    <AwarenessCmdOption>{
        type: 'aw-oneshot',
        id,
        useScheduler: false, // One-shot events don't need periodic updates
        payload,
        expireAfterSeconds: 0, // Expire immediately after processing
        startAfterSeconds: 0, // Send immediately
    };

/**
 * Creates an awareness command option for notification messages
 * Used to display success, info, or error notifications to users
 *
 * @param id - Unique identifier for this awareness
 * @param option - Optional additional configuration to override defaults
 * @returns An awareness command option configured for notifications
 */
export const buildNotificationAwarenessCmdOption = (id: string, option: Partial<AwarenessCmdOption> = {}) =>
    <AwarenessCmdOption>{
        type: 'aw-notification',
        id,
        useScheduler: false, // Notifications don't need periodic updates
        expireAfterSeconds: 0, // Expire immediately after processing
        startAfterSeconds: 0, // Send immediately
        isNotSendToLocalReceiver: false, // Local should receive notification message by default
        isNotSendToRemoteReceiver: true, // Remote should not receive notification message by default
        ...option, // Allow overriding defaults
    };

/**
 * Creates an awareness command option for loading events
 * Used for long-running operations like pasting documents or processing data
 *
 * @param id - Unique identifier for this awareness
 * @param payload - Optional additional data to include
 * @returns An awareness command option configured for loading events
 */
export const buildLoadingAwarenessCmdOption = (id: string, payload?: any) =>
    <AwarenessCmdOption>{
        type: 'aw-loading',
        id,
        useScheduler: true, // Loading events need periodic updates to maintain visibility
        payload,
        expireAfterSeconds: 1, // Expire shortly after the last update
        startAfterSeconds: 5, // Start showing after a delay to avoid flashing for quick operations
    };

/**
 * Sends a notification message to a specific viewport
 * Convenience function for displaying success, error, or info messages
 *
 * @param awFeature - The awareness feature to use for sending
 * @param viewportId - ID of the viewport to send the notification to
 * @param message - The notification message to display
 * @param msgType - Type of notification (success, error, or info)
 * @param options - Optional additional configuration
 * @returns The ID of the created awareness
 */
export async function sendNotiMessage(
    awFeature: AwarenessFeature,
    viewportId: string,
    message: string,
    msgType: 'success' | 'error' | 'info' = 'success',
    options?: Partial<AwarenessCmdOption>
) {
    return awFeature.sendAwarenessCommand(
        viewportId,
        message,
        buildNotificationAwarenessCmdOption('aw-default-noti-msg', {
            ...options,
            payload: { msgType },
        })
    );
}

/**
 * Feature that manages awareness across multiple viewports
 *
 * Handles coordination of awareness tools, garbage collection of expired awareness states,
 * and provides methods for sending and receiving awareness commands.
 *
 * This feature supports various types of awareness including:
 * - Presenter mouse movements
 * - Document editing actions
 * - Loading indicators
 * - Notification messages
 */
export class AwarenessFeature {
    /**
     * Observable map of all registered awareness tools by viewport ID
     */
    protected readonly tools$: BehaviorSubject<Map<ViewportId, AwarenessTool>> = new BehaviorSubject(new Map());

    /**
     * Creates a new AwarenessFeature
     *
     * @param coord - The editor coordinator that manages this feature
     */
    constructor(private coord: EditorCoordinator) {
        // Register event listener to handle editor additions
        this.coord.registerCoordEventListener(new this.CoordinatorEventHandler(this));
    }

    /**
     * Event handler for coordinator events
     * Handles initialization of the awareness feature in editors
     *
     * @private
     */
    private CoordinatorEventHandler = class implements VEventListener<CoordinatorEvent> {
        /**
         * Creates a new coordinator event handler
         *
         * @param featureInstance - The awareness feature instance
         */
        constructor(private featureInstance: AwarenessFeature) {}

        /**
         * Handles coordinator events
         * Currently only handles editor-added events to initialize the awareness feature
         *
         * @param eventData - The event data from the coordinator
         * @returns The event data, possibly modified
         */
        onEvent(eventData: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
            switch (eventData.eventType) {
                case 'editor-added': {
                    const editorInstance = (eventData.state as EditorAddedCES).editor;
                    // Initialize the awareness feature in the editor if it supports it
                    if (editorInstance.isSupportFeature(FEATURE_AWARENESS)) {
                        editorInstance.onFeatureInitialization(FEATURE_AWARENESS, this.featureInstance);
                    }
                    break;
                }
                default:
                    break;
            }
            return eventData;
        }
    };

    /**
     * Creates an interval that periodically checks for and removes expired awareness states
     * This prevents the accumulation of stale awareness information
     *
     * @private
     */
    private garbageCollectorIntervalId: number | undefined;
    private startGarbageCollector() {
        this.garbageCollectorIntervalId = setInterval(() => {
            for (const tool of this.tools$.value.values()) {
                const receivedAwareness$ = tool.toolState.receivedAwareness;
                // Filter out expired awareness states
                const filteredAwareness = receivedAwareness$.value.filter(
                    awareness => !this.hasAwarenessExpired(awareness)
                );

                // Update the list only if any expired states were removed
                if (receivedAwareness$.value.length !== filteredAwareness.length) {
                    receivedAwareness$.next(filteredAwareness);
                }
            }
        }, 1000); // Run every second
    }

    /**
     * Determines if an awareness state has expired based on its timestamp and expiration setting
     *
     * @param awareness - The awareness state to check
     * @returns True if the awareness has expired, false otherwise
     * @private
     */
    private hasAwarenessExpired = (awareness: Awareness): boolean => {
        const expireAfterSeconds = awareness.options.expireAfterSeconds;
        // Never expire if expireAfterSeconds is null, undefined, or negative
        if (_.isNil(expireAfterSeconds) || expireAfterSeconds < 0) return false;
        // Check if the time elapsed since the last update exceeds the expiration time
        return performance.now() - awareness.updatedAt >= expireAfterSeconds * 1000;
    };

    /**
     * Registers an awareness tool for a specific viewport
     * Each viewport has its own awareness tool to manage its awareness states
     *
     * @param viewportId - ID of the viewport
     * @param toolInstance - The awareness tool to register
     */
    addAwarenessTool(viewportId: ViewportId, toolInstance: AwarenessTool) {
        // Add the tool to the map
        this.tools$.value.set(viewportId, toolInstance);
        // Notify subscribers of the change
        this.tools$.next(this.tools$.value);
        // Start the garbage collector if this is the first tool
        if (this.tools$.value.size === 1) this.startGarbageCollector();
    }

    /**
     * Retrieves the awareness tool for a specific viewport
     *
     * @param viewportId - ID of the viewport
     * @returns The awareness tool for the viewport, or undefined if not found
     */
    getAwarenessTool(viewportId: ViewportId) {
        return this.tools$.value.get(viewportId);
    }

    /**
     * Removes an awareness tool for a specific viewport
     * Called when a viewport is destroyed or no longer needs awareness
     *
     * @param viewportId - ID of the viewport
     */
    removeAwarenessTool(viewportId: ViewportId) {
        if (this.tools$.value.has(viewportId)) {
            // Remove the tool from the map
            this.tools$.value.delete(viewportId);
            // Notify subscribers of the change
            this.tools$.next(this.tools$.value);
        }
        // Stop the garbage collector if there are no more tools
        if (this.tools$.value.size === 0 && this.garbageCollectorIntervalId) {
            clearInterval(this.garbageCollectorIntervalId);
            this.garbageCollectorIntervalId = undefined;
        }
    }

    /**
     * Retrieves the received awareness states for a specific viewport
     *
     * @param viewportId - ID of the viewport
     * @returns An observable of awareness states for the viewport, or undefined if not found
     */
    getReceivedAwareness(viewportId: ViewportId): BehaviorSubject<Awareness[]> | undefined {
        return this.tools$.value.get(viewportId)?.toolState?.receivedAwareness;
    }

    /**
     * Sends an awareness command to a specific viewport
     *
     * @param viewportId - ID of the viewport to send to
     * @param message - Human-readable message describing the awareness state
     * @param options - Configuration options for this awareness
     * @returns The ID of the created awareness, or null if the tool wasn't found
     */
    sendAwarenessCommand(viewportId: string, message: string, options?: AwarenessCmdOption): Promise<string | null> {
        const tool = this.getAwarenessTool(viewportId);
        if (!tool) {
            console.error('Awareness tool not found');
            return Promise.resolve(null);
        }
        return tool.sendAwarenessState(message, options);
    }

    /**
     * Clears an awareness command from a specific viewport
     *
     * @param viewportId - ID of the viewport containing the awareness
     * @param awarenessId - ID of the awareness to clear
     * @returns True if the awareness was cleared, false otherwise
     */
    async clearAwarenessCommand(viewportId: string, awarenessId: string): Promise<boolean> {
        const tool = this.getAwarenessTool(viewportId);
        if (!tool) {
            console.error('Awareness tool not found');
            return false;
        }
        return (await tool.clearAwarenessState(awarenessId)) ?? false;
    }

    /**
     * Sends an awareness message, executes a callback, then optionally clears the awareness
     * Useful for long-running operations that need to show progress and then clean up
     *
     * @param viewportId - ID of the viewport to send to
     * @param message - Human-readable message describing the awareness state
     * @param options - Configuration options for this awareness
     * @param cb - Optional callback function to execute while the awareness is active
     * @param shouldClearAfter - Whether to clear the awareness after the callback completes
     * @returns The result of the callback, if any
     * @throws Error if the awareness tool is not found or if the callback throws
     */
    async useAwareness(
        viewportId: string,
        message: string,
        options?: AwarenessCmdOption,
        cb?: () => Promise<any>,
        shouldClearAfter = true
    ) {
        // Get the tool reference to ensure it exists and to avoid it being removed
        // before the clear command is sent
        const tool = this.getAwarenessTool(viewportId);
        if (!tool) throw new Error('Awareness tool not found');

        // Send the awareness state
        const awarenessId = await tool.sendAwarenessState(message, options);

        try {
            const result = cb ? await cb() : undefined;
            shouldClearAfter && (await tool.clearAwarenessState(awarenessId));
            return result;
        } catch (error) {
            shouldClearAfter && (await tool.clearAwarenessState(awarenessId));
            throw error;
        }
    }
}
