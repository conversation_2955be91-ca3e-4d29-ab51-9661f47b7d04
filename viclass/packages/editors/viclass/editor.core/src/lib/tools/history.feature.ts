import { <PERSON><PERSON><PERSON>, EditorAddedCES, EditorCoordinator, VEventListener, ViewportId } from '../api';
import { HistoryManager } from './history.manager';
import { HistoryTool, SupportFeatureHistory } from './history.tool';

export const FEATURE_HISTORY = 'common_history_feature';

/**
 *
 * <AUTHOR>
 */
export class HistoryFeature {
    protected readonly historyManagers: Map<ViewportId, HistoryManager> = new Map();
    protected readonly historyTools: Map<ViewportId, HistoryTool> = new Map();

    constructor(protected coord: EditorCoordinator) {
        // listen to the coordinator event to determine what to do with viewports
        this.coord.registerCoordEventListener(new this._COORD_EVENT_HANDLER(this));
    }

    getHistoryManager(vpId: ViewportId): HistoryManager {
        const vm = this.coord.getViewportManager(vpId);
        if (!vm) {
            throw Error("Don't have viewport " + vpId);
        }

        let m = this.historyManagers.get(vpId);
        if (!m) {
            m = new HistoryManager();
            this.historyManagers.set(vpId, m);
        }

        return m;
    }

    addHistoryTool(vmId: ViewportId, tool: HistoryTool) {
        this.historyTools.set(vmId, tool);
    }

    removeHistoryTool(vmId: ViewportId) {
        if (this.historyTools.has(vmId)) this.historyTools.delete(vmId);
        if (this.historyManagers.has(vmId)) this.historyManagers.delete(vmId);
    }

    clear(editor: SupportFeatureHistory, viewportId: ViewportId) {
        const m = this.historyManagers.get(viewportId);

        if (m) {
            const items = m.getAllItem();
            m.clear(items);
        }

        const historyTool = this.historyTools.get(viewportId);
        historyTool?.toolbar.update('history', historyTool.toolState);
    }

    private _COORD_EVENT_HANDLER = class implements VEventListener<CoordinatorEvent> {
        constructor(private p: HistoryFeature) {}

        onEvent(eventData: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
            switch (eventData.eventType) {
                case 'viewport-added': {
                    // do nothing
                    break;
                }
                case 'viewport-removed': {
                    const eventState = eventData.state;
                    if (!eventState?.vmId) throw new Error('Unable to get viewport id when handling event.');
                    this.p.historyManagers.delete(eventState.vmId);
                    break;
                }
                case 'editor-added': {
                    const ed = (eventData.state as EditorAddedCES).editor;
                    if (ed.isSupportFeature(FEATURE_HISTORY)) {
                        ed.onFeatureInitialization(FEATURE_HISTORY, this.p);
                    }
                    break;
                }
                default:
                    break;
            }

            return eventData;
        }
    };
}
