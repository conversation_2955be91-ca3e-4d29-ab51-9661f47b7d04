import { CmdPreSyncHook, ViewportManager } from './';
import { VEventData } from './events';

export type EditorId = number;
export type DocumentId = string;
export type DocLocalId = number;
export type ViewportId = string;
export type LayerId = number;

export interface ModuleLookup {
    type?: 'module' | 'script' | 'manifest';
    remoteEntry: string;
    remoteName: string;
    exposedModule: string;
}

/**
 * Sometimes we want to configure a module as well as
 * look it up only
 */
export interface ModuleLookupWithSettings {
    lookup: ModuleLookup;
    settings?: any;
}

/**
 * Represent the kind of data that has id. This is local id, and is unique for a channel and controller type combination
 */
export interface IdAble {
    readonly id: number;
}

export interface Versionable {
    version: number;
}

export interface State extends IdAble, Versionable {}

/**
 * Origin type specifies if the command is generated locally or received from remote location.
 * Handling of local command and remote command is a little bit different. The local command is the first
 * time this command is created to modify the state of the target or create a target. This changes will need
 * to be set on the server through the editor API and coordinator API as well. Let take an example
 *
 * - A coordinator state contains a map which let us know what document is stored within the coordinator state (or the class room)
 * - The coordinator state needs to be updated when there is a new document is inserted by the editor
 *
 * In this case, when receiving a local command notifying the editor to insert a new document, the cmd processor
 * should inform the coordinator through some maner, e.g. through a local document event source (coordinator can call, for example
 * registerLocalVDocEventListener), other coordinator will also be notified through the sync of coordinator state
 */
export enum CmdOriginType {
    local = 0,
    remote = 1,
}

/**
 * A command contains the changes to be done on an entity (document). Reusing the VEventData for shortness
 */
export interface Cmd<CmdType> extends VEventData<CmdType, any, any> {
    meta: CmdMeta;

    serialize(): Uint8Array;
    deserialize(buf: Uint8Array): any;
}

export type VersionGenerator = () => number;

/**
 * Abstract command doesn't care about the sequence number of the command, command sender might or might not set the
 * sequence. If the sequence is set, it must be sent through reliable channel.
 */
export abstract class AbstractCommand<CmdType extends number> implements Cmd<CmdType> {
    source = undefined; // source is not used
    eventType: CmdType; // set by each command
    meta: CmdMeta;
    abstract state: any;

    constructor(meta: CmdMeta, eventType: CmdType) {
        if (eventType != meta.cmdType)
            throw new Error('Unmatched command type between meta data and intended cmd type');
        this.meta = meta;
        this.eventType = eventType;
    }

    get cmdType(): CmdType {
        return this.eventType;
    }

    abstract serialize(): Uint8Array;
    abstract deserialize(buf: Uint8Array): any;
}

export interface CmdStateMessage {
    serializeBinary(): Uint8Array;
}
export interface CmdStateMessageStatic<T extends CmdStateMessage> {
    new (): T;
    deserializeBinary(buf: Uint8Array): T;
}

export function cmdMeta(vm: ViewportManager, targetId: number, cmdTypeCode: number): CmdMeta {
    return {
        channelCode: -1, // default, when sent through a channel, the code will be assigned
        cmdType: cmdTypeCode,
        targetId: targetId,
        origin: CmdOriginType.local,
        viewport: vm,
    };
}

/**
 * Create the meta data for a reliable command
 *
 * A reliable command is a command that must be sent reliably. It forces the user to supply a sequence
 * when create the command. Since the sequence is set, these commands are always sent through the reliable channel.
 *
 *
 * A message sender might combine multiple command and sent as one single reliable command.
 *
 * For example, in the div editor, the update div location command is debounced and only the last one will be sent.
 *
 * currentVersion is an object that contains the sequence of the entity that this command is modifying.
 * Every command created will cause this number to increase by one.
 *
 * docId is the id of the document whose state is being modified
 */
export function reliableSaveCmdMeta(
    vm: ViewportManager,
    currentVersion: Versionable | VersionGenerator | number,
    docId: number,
    targetId: number,
    cmdTypeCode: number,
    isolateCombinator: boolean = false
): CmdMeta {
    const meta = cmdMeta(vm, targetId, cmdTypeCode);

    let seq: number;

    if (typeof currentVersion === 'function') seq = currentVersion();
    else if (typeof currentVersion === 'number') seq = currentVersion;
    else {
        seq = ++currentVersion.version; // auto increment the version of the versionable
    }

    meta.reliable = true;
    meta.sequence = seq;
    meta.versionable = docId;
    meta.isolateCombinator = isolateCombinator;

    return meta;
}

export function reliableCmdMeta(
    vm: ViewportManager,
    docId: number,
    targetId: number,
    cmdTypeCode: number,
    isolateCombinator: boolean = false
): CmdMeta {
    const meta = cmdMeta(vm, targetId, cmdTypeCode);
    meta.versionable = docId;
    meta.reliable = true;
    meta.isolateCombinator = isolateCombinator;

    return meta;
}

export interface CmdMeta {
    // channel name, should be set by the CmdChannel who is going to send
    channelCode: number;

    // target id is the id of the object that this command acts on. For commands that CREATE the object, this id is the id to be used for the object
    targetId: number;

    // the local id of the entity whose version is changed, normally this is the id of the document that this command is modifying, or previewing
    versionable?: number;

    // the combination of the cmd type and target id helps the command processor to locate the exact object to apply the command
    cmdType: number;

    // whether the command is from local source or is it from a remote source
    origin: number;

    // whether to sync the command
    notSync?: boolean;

    // whether the command is sent over reliable channel. If the sequence is set, this field is understood as true
    // no matter what value it has. If sequence is not set, this field's value is effective
    reliable?: boolean;

    // the version of the versionable should be after the command is applied
    sequence?: number;

    // every command generated from users' interactions.
    // This field the viewport that the interactions originate from
    // it is the responsibility of the producer of the command
    // to fill in this field. It is not synced through network.
    viewport: ViewportManager;

    /**
     * Whether or not the synchronization to other peer should be done only when local processing is done.
     * This is needed in some cases where the local processor need to ensure states consistency so that other
     * peer can process the command consistently.
     *
     * For example, when applying the CRUD Command for create-doc. The local processor might create the document on
     * the server. In classroom, the mapping of the new doc must be set inside the classroom state before synchronizing
     * creation command to other peer. In this case, this flag can be set so that the command channel wait for the mapping
     * to be fully updated by the editor cmd processor.
     */
    waitLocalProcessingBeforeSync?: boolean;

    /**
     * Before the command is synchronized to the gateway,
     * there might be some preparation needs to be done to prepare the environment
     * in different use case scenarios.
     *
     * For example,
     *
     * In the case of classroom, an insert command must be known to CCS so that the CCS
     * can prepare the mapping and update the coordinator state accordingly BEFORE the message
     * is sent to other classroom participants (if this condition was not true, there might be
     * cases where the user joined class AFTER the insert command was sent AND BEFORE the time
     * CCS prepared the mapping, and hence he totally missed the insertion of the document!). In
     * this case, the coordinator might register the pre sync hook so that before an insert command
     * is sync, the coordinator can call the CCS and update the coordinator state fully. The early
     * implementation does this on the Syncer and it complicated the coordinator because the coordinator
     * must wait for singal messages from CCS. The pre-sync hook totally remove this signaling need and hence
     * makes the implementation of the coordinator more straight forward.
     *
     * The hook a function that accept the command and perform some logic. The logic return a value
     * or a promise. If a promise is returned, then the promise must be completed BEFORE the command is synchronized.
     */
    preSyncHooks?: CmdPreSyncHook[];

    isolateCombinator?: boolean;
}
