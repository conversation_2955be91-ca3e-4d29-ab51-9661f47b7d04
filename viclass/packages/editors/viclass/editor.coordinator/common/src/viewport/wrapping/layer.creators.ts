import {
    BoundedGraphicLayerCtrl,
    BoundedSVGLayerCtrl,
    CoverLayerCtrl,
    DOMElementLayerCtrl,
    LayerCreator,
    LayerImplementation,
    LayerOptions,
    StickyLayerCtrl,
    UnboundedGraphicLayerCtrl,
    UnboundedSVGLayerCtrl,
    VDocLayerCtrl,
} from '@viclass/editor.core';
import { NoWrapBoundedGraphicLayer } from './bounded.graphic.layer';
import { NoWrapBoundedSVGLayer } from './bounded.svg.layer';
import { DivCoverLayerCtrl } from './div.cover.layer.ctrl';
import {
    NoWrapDOMElementLayer,
    NoWrapInlineDOMElementLayer,
    NoWrapUnboundedDOMElementLayer,
} from './dom.element.layer.ctrl';
import { NoWrapStickyLayer } from './sticky.layer.ctrl';
import { NoWrapUnboundedGraphicLayerCtrl } from './unbounded.graphic.layer';
import { NoWrapUnboundedSVGLayer } from './unbounded.svg.layer';
import { WrappingBoardViewportManager } from './wrapping.board.vm';
import { WrappingInlineViewportManager } from './wrapping.inline.vm';

/**
 * List of possible layer creators supported by board viewport by default
 */
export const layerCreators = new Map<LayerImplementation<VDocLayerCtrl>, LayerCreator<VDocLayerCtrl, LayerOptions>>();

layerCreators.set(UnboundedGraphicLayerCtrl, (options?: LayerOptions): UnboundedGraphicLayerCtrl => {
    if (!options.editor || !options.state || !options.viewport)
        throw new Error(`Invalid layer creation options when creating ${UnboundedGraphicLayerCtrl.name}`);

    if (!(options.viewport instanceof WrappingBoardViewportManager))
        throw new Error(
            `Invalid viewport type. Creating ${NoWrapBoundedGraphicLayer.name} must be invoked by ${WrappingBoardViewportManager.name}.`
        );

    return new NoWrapUnboundedGraphicLayerCtrl(options.viewport, options.editor, options.state);
});

layerCreators.set(BoundedGraphicLayerCtrl, (options?: LayerOptions): BoundedGraphicLayerCtrl => {
    if (!options.editor || !options.state || !options.viewport || !options.boundary)
        throw new Error(`Invalid layer creation options when creating ${BoundedGraphicLayerCtrl.name}`);

    if (!(options.viewport instanceof WrappingBoardViewportManager))
        throw new Error(
            `Invalid viewport type. Creating ${NoWrapBoundedGraphicLayer.name} must be invoked by ${WrappingBoardViewportManager.name}.`
        );

    return new NoWrapBoundedGraphicLayer(options.boundary, options.viewport, options.state, options.editor);
});

layerCreators.set(UnboundedSVGLayerCtrl, (options?: LayerOptions): UnboundedSVGLayerCtrl => {
    if (!options.viewport)
        throw new Error(`Invalid layer creation options when creating ${UnboundedSVGLayerCtrl.name}`);

    if (!(options.viewport instanceof WrappingBoardViewportManager))
        throw new Error(
            `Invalid viewport type. Creating ${NoWrapUnboundedSVGLayer.name} must be invoked by ${WrappingBoardViewportManager.name}.`
        );

    return new NoWrapUnboundedSVGLayer(options.viewport, options.state, options.editor);
});

layerCreators.set(BoundedSVGLayerCtrl, (options?: LayerOptions): BoundedSVGLayerCtrl => {
    if (!options.editor || !options.state || !options.viewport || !options.boundary)
        throw new Error(`Invalid layer creation options when creating ${BoundedSVGLayerCtrl.name}`);

    if (!(options.viewport instanceof WrappingBoardViewportManager))
        throw new Error(
            `Invalid viewport type. Creating ${NoWrapBoundedSVGLayer.name} must be invoked by ${WrappingBoardViewportManager.name}.`
        );

    return new NoWrapBoundedSVGLayer(options.boundary, options.viewport, options.state, options.editor);
});

layerCreators.set(CoverLayerCtrl, (options?: LayerOptions): CoverLayerCtrl => {
    if (!options.viewport)
        throw new Error(`Invalid layer creation options when creating ${CoverLayerCtrl.name}. No viewport specified.`);

    if (!(options.viewport instanceof WrappingBoardViewportManager))
        throw new Error(
            `Invalid viewport type. Creating ${CoverLayerCtrl.name} must be invoked by ${WrappingBoardViewportManager.name}.`
        );

    return new DivCoverLayerCtrl(options.viewport, options.editor, options.state);
});

layerCreators.set(DOMElementLayerCtrl, (options?: LayerOptions): DOMElementLayerCtrl => {
    if (!options.viewport)
        throw new Error(
            `Invalid layer creation options when creating ${DOMElementLayerCtrl.name}. No viewport specified.`
        );

    if (
        !(options.viewport instanceof WrappingBoardViewportManager) &&
        !(options.viewport instanceof WrappingInlineViewportManager)
    )
        throw new Error(
            `Invalid viewport type. Creating ${DOMElementLayerCtrl.name} must be invoked by ${WrappingBoardViewportManager.name} or ${WrappingInlineViewportManager.name}.`
        );

    if (options.viewport instanceof WrappingInlineViewportManager)
        return new NoWrapInlineDOMElementLayer(options.viewport, options.editor, options.state, options.nativeEl);
    else if (options.docViewMode === 'full-viewport')
        return new NoWrapUnboundedDOMElementLayer(options.viewport, options.editor, options.state, options.domElType);
    else
        return new NoWrapDOMElementLayer(
            options.boundary,
            options.state,
            options.editor,
            options.viewport,
            options.domElType
        );
});

layerCreators.set(StickyLayerCtrl, (options: LayerOptions): NoWrapStickyLayer => {
    if (!options.viewport || !options.style)
        throw new Error(`Invalid layer creation options when creating ${NoWrapStickyLayer.name}`);

    if (!(options.viewport instanceof WrappingBoardViewportManager))
        throw new Error(
            `Invalid viewport type. Creating ${NoWrapStickyLayer.name} must be invoked by ${WrappingBoardViewportManager.name}.`
        );

    return new NoWrapStickyLayer(options.viewport, options.style);
});
