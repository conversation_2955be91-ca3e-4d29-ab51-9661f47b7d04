/**
 *  A light wrapper around html-to-image library to export html elements to image.
 *  Add support for:
 *  - exporting SVGElement contains foreignObject to image.
 *  - exporting multiple elements to a single image.
 */

import { ExportTarget, FEATURE_HTML_EXPORT, SupportHtmlExportFeature } from '@viclass/editor.core';
import { getFontEmbedCSS, toCanvas, toSvg } from 'lib-html-to-image';
import { cloneNode } from 'lib-html-to-image/clone-node';
import { Options } from 'lib-html-to-image/types';
import { canvasToBlob, checkCanvasDimensions, createImage, svgToDataURL } from 'lib-html-to-image/util';
import { BaseCoordinator } from '../base.coordinator';
import { loadCSS, loadMathCss } from './css-utils';

export type ExportOptions = Options & {
    imageProxyPrefix?: string;
};

export type PdfExportOptions = ExportOptions & {
    paperSize?: PageSize;
    orientation?: Orientation;
    autoDetectBestSize?: boolean;
};

type Size = {
    width: number;
    height: number;
};

export type PageSize = 'A4' | 'Letter' | 'A3' | 'A5';
export type Orientation = 'portrait' | 'landscape';

/**
 * Standard print page sizes in millimeters.
 * These sizes are used to set the @page size in the iframe's print CSS.
 */
const printPageSizes: Record<PageSize, Size> = {
    A4: { width: 210, height: 297 },
    Letter: { width: 215.9, height: 279.4 },
    A3: { width: 297, height: 420 },
    A5: { width: 148, height: 210 },
};

const DEBUG_PRINT = false; // Set to true to enable debug print styles and open iframe content in new tab
const DEBUG_PRINT_CSS = '* { border: 1px solid red !important; }';

/**
 *  Create a canvas element apply options (fallback to the element real size).
 */
function createCanvas(
    options: ExportOptions,
    elementSize: Size
): { canvas: HTMLCanvasElement; context: CanvasRenderingContext2D } {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d')!;
    const ratio = options.pixelRatio || 1;
    const canvasWidth = options.canvasWidth || elementSize.width;
    const canvasHeight = options.canvasHeight || elementSize.height;

    canvas.width = canvasWidth * ratio;
    canvas.height = canvasHeight * ratio;

    if (!options.skipAutoScale) {
        checkCanvasDimensions(canvas);
    }
    canvas.style.width = `${canvasWidth}`;
    canvas.style.height = `${canvasHeight}`;

    if (options.backgroundColor) {
        context.fillStyle = options.backgroundColor;
        context.fillRect(0, 0, canvas.width, canvas.height);
    }

    return { canvas, context };
}

/**
 *  Convert SVGElement to canvas along with its foreignObject children.
 */
async function svgToCanvas(element: SVGElement, options: ExportOptions = {}): Promise<HTMLCanvasElement> {
    const svgData = await svgToDataURL(element);
    const img = await createImage(svgData);

    const rect = element.getBoundingClientRect();
    const { canvas, context } = createCanvas(options, rect);

    // draw foreignObject children in order
    const foreignObjs = element.querySelectorAll('foreignObject');
    for (const foreignObj of Array.from(foreignObjs)) {
        for (const child of Array.from(foreignObj.children)) {
            const childSource = await toCanvas(child as unknown as HTMLElement);

            const innerRect = child.getBoundingClientRect();
            context.drawImage(
                childSource,
                innerRect.left - rect.left,
                innerRect.top - rect.top,
                childSource.width,
                childSource.height
            );
        }
    }

    context.drawImage(img, 0, 0, canvas.width, canvas.height);

    return canvas;
}

/**
 *  Convert a single HTML element to an image Blob.
 */
export async function htmlToCanvas(
    element: HTMLElement | SVGElement,
    options: ExportOptions = {}
): Promise<HTMLCanvasElement> {
    if (!element) return Promise.resolve(null);

    if (options.imageProxyPrefix?.length) {
        const oldCustomElementSelectors = options.customElementSelectors || [];
        const oldCustomElementConverter = options.customElementConverter;

        options.customElementSelectors = [...oldCustomElementSelectors];
        if (!options.customElementSelectors.includes('img')) options.customElementSelectors.push('img');

        options.customElementConverter = async (node: HTMLElement) => {
            // Proxy image src to bypass CORS
            if (node.tagName === 'IMG') {
                const img = node.cloneNode(true) as HTMLImageElement;
                const url = new URL(img.src, window.location.origin);
                const isViclassDomain = url.hostname === 'viclass.vn' || url.hostname.endsWith('.viclass.vn');
                if (img.src.startsWith('http') && !img.src.startsWith(options.imageProxyPrefix) && !isViclassDomain) {
                    img.src = options.imageProxyPrefix + img.src;
                }

                return oldCustomElementSelectors.includes('img') && oldCustomElementConverter
                    ? (oldCustomElementConverter(img) ?? img)
                    : img;
            } else return oldCustomElementConverter ? oldCustomElementConverter(node) : null;
        };
    }

    return element.tagName === 'SVG'
        ? await svgToCanvas(element as SVGElement, options)
        : await toCanvas(element as HTMLElement, options);
}

export async function htmlToBlob(element: HTMLElement | SVGElement, options: ExportOptions = {}): Promise<Blob> {
    const canvas = await htmlToCanvas(element, options);
    return canvasToBlob(canvas, options);
}

/**
 *  Calculate the total boundary of multiple elements.
 */
function combineBoundary(elements: (HTMLElement | SVGElement)[]) {
    const combinedBoundary = {
        top: Infinity,
        right: -Infinity,
        bottom: -Infinity,
        left: Infinity,
        width: 0,
        height: 0,
    };

    elements.forEach(element => {
        const rect = element.getBoundingClientRect();
        combinedBoundary.top = Math.min(combinedBoundary.top, rect.top);
        combinedBoundary.right = Math.max(combinedBoundary.right, rect.right);
        combinedBoundary.bottom = Math.max(combinedBoundary.bottom, rect.bottom);
        combinedBoundary.left = Math.min(combinedBoundary.left, rect.left);
    });

    combinedBoundary.width = combinedBoundary.right - combinedBoundary.left;
    combinedBoundary.height = combinedBoundary.bottom - combinedBoundary.top;

    return combinedBoundary;
}

/**
 *  Convert multiple HTML elements to a single image Blob.
 */
export async function combineHtmlToBlob(
    elements: (HTMLElement | SVGElement)[],
    options: ExportOptions = {}
): Promise<Blob> {
    if (!elements || !elements.length) return Promise.resolve(null);

    // create separated image/canvas for each element
    const svgs: (CanvasImageSource | null)[] = await Promise.all(
        elements.map(async el => {
            if (el instanceof SVGElement) {
                return svgToCanvas(el, options);
            }

            // use native size of element and transparent background
            const svgUrl = await toSvg(el, {
                ...options,
                backgroundColor: 'transparent',
                width: undefined,
                height: undefined,
            });
            return svgUrl ? createImage(svgUrl) : null;
        })
    );

    const boundary = combineBoundary(elements);
    const { canvas, context } = createCanvas(options, boundary);

    svgs.forEach((svg, index) => {
        if (!svg) return;
        const rect = elements[index].getBoundingClientRect();
        const offsetX = rect.left - boundary.left;
        const offsetY = rect.top - boundary.top;

        context.drawImage(svg, offsetX, offsetY, rect.width, rect.height);
    });

    return canvasToBlob(canvas, options);
}

/**
 * Prints the content of an iframe using the `window.print()` method.
 *
 * @param {HTMLIFrameElement} iframe - The iframe element to print.
 * @param {boolean} isContainer - Whether the target element is a container with child elements to measure.
 */
export function iframeToPDF(
    iframe: HTMLIFrameElement,
    paperSize: PageSize = 'A4',
    orientation: Orientation = 'portrait'
) {
    // 1. Get the iframe’s contentWindow and document
    const iframeWindow = iframe.contentWindow;
    if (!iframeWindow) {
        console.warn('iframe has no contentWindow; calling print() directly.');
        return;
    }
    const doc = iframeWindow.document;

    // 2. Inject CSS into the iframe to set page size (without margin) and hide scrollbars when printing
    const styleTag = doc.createElement('style');
    styleTag.innerHTML = `
    @media print {
      @page {
        size: ${paperSize} ${orientation};
        margin: 0;
      }
      html, body {
        margin: 0;
        padding: 0;
        overflow: hidden !important;
      }
      ${DEBUG_PRINT ? DEBUG_PRINT_CSS : ''}
    }
  `;
    const headOrHtml = doc.head || doc.documentElement;
    headOrHtml.appendChild(styleTag);

    // 3. Get standard paper dimensions in millimeters
    const chosen = printPageSizes[paperSize] || printPageSizes['A4'];

    // 4. Determine the page’s effective width in millimeters (no margins)
    const pageWidthMM = orientation === 'landscape' ? chosen.height : chosen.width;

    // 5. Create a temporary div to measure how many pixels correspond to pageWidthMM
    const tester = doc.createElement('div');
    tester.style.width = pageWidthMM + 'mm';
    tester.style.position = 'absolute';
    tester.style.visibility = 'hidden';
    doc.body.appendChild(tester);

    // 6. Read the measured pixel width and remove the tester div
    const printablePx = tester.offsetWidth;
    doc.body.removeChild(tester);

    // 7. Get the target element inside the iframe body
    const targetEl = doc.body.firstElementChild as HTMLElement | null;
    if (!targetEl) {
        console.warn('No element found inside iframe; calling print() directly.');
        styleTag.remove();
        iframeWindow.print();
        return;
    }

    // 8. Measure the element's original width and height in pixels
    let elWidthPx = 0;
    let elHeightPx = 0;
    Array.from(targetEl.children).forEach(child => {
        const childEl = child as HTMLElement;
        const childRect = {
            width: childEl.offsetWidth,
            height: childEl.offsetHeight,
            left: childEl.offsetLeft,
            top: childEl.offsetTop,
        };

        elWidthPx = Math.max(elWidthPx, childRect.left + childRect.width);
        elHeightPx = Math.max(elHeightPx, childRect.top + childRect.height);
    });

    if (elWidthPx > printablePx) {
        // 9. Calculate the scale ratio: shrink only if the element is wider than the page
        const scaleRatio = printablePx / elWidthPx;

        // 10. Compute the element’s dimensions after scaling
        const scaledWidthPx = elWidthPx * scaleRatio;
        const scaledHeightPx = elHeightPx * scaleRatio;

        // 11. Apply CSS transform to scale the element and set its origin to top-left
        targetEl.style.transformOrigin = 'top left';
        targetEl.style.transform = `scale(${scaleRatio})`;

        // 12. Set the iframe body’s width and height to match the scaled element size
        doc.body.style.width = `${scaledWidthPx}px`;
        doc.body.style.height = `${scaledHeightPx}px`;
        doc.body.style.overflow = 'hidden';
    }

    // 13. Call print() (no afterprint cleanup needed because the iframe will be discarded)
    iframeWindow.print();
}

// Util function for testing purposes: opens the iframe content in a new tab along with all styles
async function openIframeInNewTab(iframe: HTMLIFrameElement): Promise<void> {
    // Get the iframe's document content as HTML
    const iframeDoc = iframe.contentDocument;
    if (!iframeDoc) return;

    // Open a new window and clone the iframe's document content into it
    const newWindow = window.open('', '_blank');
    if (newWindow) {
        // Clear the new window's document
        newWindow.document.documentElement.innerHTML = '';

        // Clone and import the <head> content (styles, etc.)
        const headNodes = Array.from(iframeDoc.head.childNodes);
        headNodes.forEach(node => {
            newWindow.document.head.appendChild(node.cloneNode(true));
        });

        // Clone and import the <body> content
        const bodyNodes = Array.from(iframeDoc.body.childNodes);
        bodyNodes.forEach(node => {
            newWindow.document.body.appendChild(node.cloneNode(true));
        });
        newWindow.document.body.style.width = iframeDoc.body.style.width;
        newWindow.document.body.style.height = iframeDoc.body.style.height;
    }
}

async function waitNextFrame(): Promise<void> {
    await new Promise(resolve => requestAnimationFrame(resolve));
}

/**
 * Converts a list of HTML elements to a PDF using an iframe. The elements are treated as layers
 * that should be positioned relative to the first element and maintain their original offsets.
 * All elements are positioned at the top-left of the document to prevent clipping during print.
 *
 * @param {HTMLElement[]} elements - The list of HTML elements to print as layers.
 * @param {PdfExportOptions} options - Export options including paper size and orientation.
 */
export async function htmlListToPDF(elements: HTMLElement[], options: PdfExportOptions = {}): Promise<void> {
    if (!elements || elements.length === 0) {
        console.warn('No elements provided for PDF export.');
        return;
    }

    const { paperSize = 'A4', orientation = 'portrait', autoDetectBestSize = true, ...cloneOptions } = options;

    // Use the first element as reference for sizing
    const referenceElement = elements[0];

    // Check if any element already contains an iframe
    const iframe = referenceElement.querySelector('iframe') as HTMLIFrameElement;
    if (iframe) {
        iframeToPDF(iframe, paperSize, orientation);
        return;
    }

    // Create a temporary iframe
    const tempIframe = document.createElement('iframe') as HTMLIFrameElement;
    tempIframe.style.visibility = 'hidden';
    tempIframe.style.position = 'absolute';
    tempIframe.style.left = '-9999px';
    tempIframe.style.top = '-9999px';
    document.body.appendChild(tempIframe);

    const iframeDoc = tempIframe.contentDocument;

    // Get the original positions and dimensions of all elements
    const elementInfos = elements.map(element => {
        const rect = element.getBoundingClientRect();
        return {
            element,
            rect,
            inlineWidth: element.style.getPropertyValue('width'),
            inlineWidthPriority: element.style.getPropertyPriority('width'),
            inlineHeight: element.style.getPropertyValue('height'),
            inlineHeightPriority: element.style.getPropertyPriority('height'),
        };
    });

    // Calculate the reference position (top-left of the first element)
    const referenceRect = elementInfos[0].rect;
    const referenceLeft = referenceRect.left;
    const referenceTop = referenceRect.top;

    // Temporarily adjust the reference element size for cloning
    const defaultWidth = autoDetectBestSize ? bestWidthForElement(referenceElement) : 'auto';
    referenceElement.style.setProperty('width', defaultWidth, 'important');

    await waitNextFrame();
    const defaultHeight = autoDetectBestSize ? bestHeightForElement(referenceElement) : 'auto';
    referenceElement.style.setProperty('height', defaultHeight, 'important');

    await waitNextFrame();

    // Create a container element to hold all cloned elements
    const containerElement = document.createElement('div');
    containerElement.style.position = 'relative';
    containerElement.style.width = 'fit-content';
    containerElement.style.height = 'fit-content';

    // Clone and position each element
    const clonePromises = elementInfos.map(async (info, index) => {
        const clonedElement = await cloneNode(info.element, cloneOptions, true);

        // Reset transform and position to avoid layout issues
        Object.assign(clonedElement.style, {
            transform: 'none',
            position: 'absolute',
            left: `${info.rect.left - referenceLeft}px`,
            top: `${info.rect.top - referenceTop}px`,
            zIndex: index.toString(), // Maintain stacking order
        });

        return clonedElement;
    });

    const clonedElements = await Promise.all(clonePromises);

    // Add all cloned elements to the container
    clonedElements.forEach(clonedElement => {
        containerElement.appendChild(clonedElement);
    });

    // Restore original styles for all elements
    elementInfos.forEach(info => {
        info.element.style.setProperty('width', info.inlineWidth, info.inlineWidthPriority);
        info.element.style.setProperty('height', info.inlineHeight, info.inlineHeightPriority);
    });

    // Append the container to the iframe
    iframeDoc.body.appendChild(containerElement);

    // Load necessary styles and fonts
    const loadStylePromises: Promise<any>[] = [];

    // Check if any element contains math-field
    const hasMathField = elements.some(element => !!element.querySelector('math-field'));
    if (hasMathField) {
        loadStylePromises.push(loadMathCss(iframeDoc));
    }

    // Check if any element contains word doc
    const hasWordDoc = elements.some(element => !!element.querySelector('.vi-word-doc'));
    if (hasWordDoc) {
        loadStylePromises.push(loadCSS(`${location.origin}/modules/themes/vi.theme.word.css`, iframeDoc));
    }

    // Embed necessary fonts (check all elements)
    const allFontPromises = elements.map(element => getFontEmbedCSS(element, {}));
    const allEmbeddedFonts = await Promise.all(allFontPromises);
    const combinedFonts = allEmbeddedFonts.filter(fonts => fonts).join('\n');

    if (combinedFonts) {
        const fontStyle = iframeDoc.createElement('style');
        const fontContent = iframeDoc.createTextNode(combinedFonts);
        fontStyle.appendChild(fontContent);
        iframeDoc.head.appendChild(fontStyle);

        loadStylePromises.push(iframeDoc.fonts.ready);
    }

    if (loadStylePromises.length) await Promise.all(loadStylePromises);

    // Print the iframe
    iframeToPDF(tempIframe, paperSize, orientation);

    // FOR TESTING ONLY: open the iframe in a new tab
    if (DEBUG_PRINT) await openIframeInNewTab(tempIframe);

    // Remove the temporary iframe
    setTimeout(() => tempIframe.remove());
}

function bestWidthForElement(element: HTMLElement): string {
    const posibleFixedSizeElSelectors = [
        '.viewport-root-el',
        '.LexicalEditorTheme__layoutContainer',
        '.LexicalEditorTheme__table',
    ];

    let maxSizePx = Math.max(element.scrollWidth, element.offsetWidth);
    for (const selector of posibleFixedSizeElSelectors) {
        element.querySelectorAll(selector).forEach(el => {
            const elem = el as HTMLElement;
            maxSizePx = Math.max(maxSizePx, elem.scrollWidth, elem.offsetWidth);
        });
    }

    return maxSizePx > 0 && maxSizePx > element.offsetWidth ? maxSizePx + 'px' : 'auto';
}

function bestHeightForElement(element: HTMLElement): string {
    const posibleFixedSizeElSelectors = ['.viewport-root-el', '.vi-word-doc'];

    let maxSizePx = Math.max(element.scrollHeight, element.offsetHeight);
    for (const selector of posibleFixedSizeElSelectors) {
        element.querySelectorAll(selector).forEach(el => {
            const elem = el as HTMLElement;
            maxSizePx = Math.max(maxSizePx, elem.scrollHeight, elem.offsetHeight);
        });
    }

    return maxSizePx > 0 && maxSizePx > element.offsetHeight ? maxSizePx + 'px' : 'auto';
}

/**
 * Util function that will execute the callback on the next frame and return the completion promise.
 *
 * On prepare to export HTML, we might do something that lead to layout changes (ex: change Viewport mode)
 * -> some sizing become unstable (ex: math-field element can temporarily re-render and become a blank space)
 * -> use this to wait for the re-render to finish before execute the node clone and export html
 */
export function executeOnNextFrame<T>(callback: () => Promise<T>): Promise<T> {
    return new Promise<T>((rs, rj) => {
        requestAnimationFrame(async () => {
            try {
                const result = await callback();
                rs(result);
            } catch (err) {
                rj(err);
            }
        });
    });
}

/**
 * Create custom HTML convert option by gather all custom converter of coordinator's editors
 */
export function createCoordinatorConvertOptions(
    coord: BaseCoordinator,
    target: ExportTarget = 'IMAGE'
): Partial<Options> {
    const selectors = new Set<string>();
    coord.editors.forEach(editor => {
        if (editor.isSupportFeature(FEATURE_HTML_EXPORT)) {
            const htmlExportFeature = editor.featureSupporter<SupportHtmlExportFeature>(FEATURE_HTML_EXPORT);
            if (htmlExportFeature) {
                htmlExportFeature.getCustomHtmlExportSelectors(target).forEach(selector => selectors.add(selector));
            }
        }
    });

    const customElementConverter = async (node: HTMLElement): Promise<HTMLElement | null> => {
        if (selectors.size === 0) return null;

        for (const [, editor] of coord.editors) {
            if (editor.isSupportFeature(FEATURE_HTML_EXPORT)) {
                const htmlExportFeature = editor.featureSupporter<SupportHtmlExportFeature>(FEATURE_HTML_EXPORT);
                if (
                    htmlExportFeature &&
                    typeof htmlExportFeature.customHtmlExportConverter === 'function' &&
                    htmlExportFeature.getCustomHtmlExportSelectors(target).some(selector => node.matches(selector))
                ) {
                    // here we run the converter one-by-one to prevent the a converter running multiple times
                    // because word editor will trigger converters of all sub-editors
                    const converted = await htmlExportFeature.customHtmlExportConverter(node, target);
                    if (converted) return converted;
                }
            }
        }

        return null;
    };

    return {
        customElementSelectors: [...selectors],
        customElementConverter,
    };
}
