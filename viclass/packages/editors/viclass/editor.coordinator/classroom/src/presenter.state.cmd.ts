import { AbstractCommand, Cmd, CmdMeta } from '@viclass/editor.core';
import { CmdTypeProto, PositionProto, PresenterProto, SizeProto, SyncPresenterProto } from '@viclass/proto/editor.core';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { PresenterState } from './data.model';

export class SyncPresenterCmd extends AbstractCommand<CmdTypeProto> {
    state: SyncPresenterProto;

    constructor(meta: CmdMeta) {
        super(meta, CmdTypeProto.SYNC_PRESENTER);
        this.state = new SyncPresenterProto();
    }

    setCoordId(coordId: string): void {
        this.state.setCoordinatorId(coordId);
    }

    setPresenter(presenter: PresenterState): void {
        const builder = new PresenterBuilder();
        const presenterProto = builder.buildPresenter(presenter);
        this.state.setPresenter(presenterProto);
    }

    deserialize(buf: Uint8Array): SyncPresenterProto {
        return SyncPresenterProto.deserializeBinary(buf);
    }

    serialize(): Uint8Array {
        return this.state.serializeBinary();
    }
}

export class PresenterBuilder {
    state: PresenterProto = new PresenterProto();

    buildPresenter(presenter: PresenterState): PresenterProto {
        if (presenter.vpPos) this.state.setVpPos(new PositionProto().setX(presenter.vpPos[0]).setY(presenter.vpPos[1]));
        if (presenter.vpZoom) this.state.setVpZoom(presenter.vpZoom);
        if (presenter.vpSize)
            this.state.setVpSize(new SizeProto().setWidth(presenter.vpSize[0]).setHeight(presenter.vpSize[1]));
        return this.state;
    }
}

export function presenterCoordCmdDeserializer(
    meta: CmdMeta,
    stateData: Uint8Array
): Cmd<CmdTypeProto | FCCmdTypeProto> {
    let cmd: Cmd<CmdTypeProto | FCCmdTypeProto>;
    const cmdType = meta.cmdType as CmdTypeProto | FCCmdTypeProto;

    switch (cmdType) {
        case CmdTypeProto.SYNC_PRESENTER: {
            cmd = new SyncPresenterCmd(meta);
            break;
        }
    }

    cmd.state = cmd.deserialize(stateData);

    return cmd;
}
