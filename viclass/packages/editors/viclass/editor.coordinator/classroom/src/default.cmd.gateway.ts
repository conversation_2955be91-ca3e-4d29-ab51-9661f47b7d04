import { SynchronizationConfig } from './data.model';
import {
    bytesToStrId,
    Cmd,
    CmdChannel,
    CmdGateway,
    CmdMeta,
    CmdOriginType,
    delayPromise,
    strIdToBytes,
    ViewportManager,
} from '@viclass/editor.core';
import { DefaultCmdChannel } from './default.cmd.channel';
import { CmdMetaProto } from '@viclass/proto/editor.core';
import { ClassroomCoordinator } from './classroom.coordinator';
import { RTCConnection } from './rtc.connection';
import { SignalMessage, SignalMessageListener, SignalMessageType } from './signal.message';

class CmdGatewayConnection {
    private reliableChannel: RTCDataChannel;
    private unreliableChannel: RTCDataChannel;

    constructor(rtcConnection: RTCConnection) {
        this.reliableChannel = rtcConnection.provideChannel('reliable');
        this.unreliableChannel = rtcConnection.provideChannel('unreliable');
    }

    sendReliable(data: Uint8Array) {
        this.reliableChannel.send(data);
    }

    sendUnreliable(data: Uint8Array) {
        this.unreliableChannel.send(data);
    }

    set onReliableMessage(cb: (ev: MessageEvent) => void) {
        this.reliableChannel.onmessage = (ev: MessageEvent) => cb(ev);
    }

    set onUnreliableMessage(cb: (ev: MessageEvent) => void) {
        this.unreliableChannel.onmessage = (ev: MessageEvent) => cb(ev);
    }
}

/**
 * Default implementation of the command gateway
 */
export class DefaultCmdGateway implements CmdGateway {
    private allowEmission: boolean = false;
    private cmdQueue: Uint8Array[] = [];
    private viewportCmdQueue: Map<string, { ready: boolean; receiveQueue: [CmdChannel, Cmd<any>][] }> = new Map();
    private channels: { [channelCode: number]: CmdChannel } = {};
    private cmdGatewayConn: CmdGatewayConnection;
    private idCache: { [id: string]: Uint8Array } = {};
    private stopped = true;

    constructor(
        private rtcConn: RTCConnection,
        private classroomCoord: ClassroomCoordinator,
        private synConf: SynchronizationConfig
    ) {}

    private onDataMessage(ev: MessageEvent<ArrayBuffer>) {
        this.receive(new Uint8Array(ev.data));
    }

    async initialize(): Promise<void> {
        this.cmdGatewayConn = new CmdGatewayConnection(this.rtcConn);
        this.cmdGatewayConn.onReliableMessage = ev => this.onDataMessage(ev);
        this.cmdGatewayConn.onUnreliableMessage = ev => this.onDataMessage(ev);
        this.rtcConn.registerListener(
            new (class implements SignalMessageListener {
                signalMessageType: SignalMessageType[] = ['RTCConnectionChange'];

                constructor(private gateway: CmdGateway) {}

                async onRequest(msg: SignalMessage): Promise<SignalMessage> {
                    throw new Error('Method unimplemented');
                }

                async onMessage(signal: SignalMessage): Promise<void> {
                    switch (signal.signalType) {
                        case 'RTCConnectionChange': {
                            if (signal.data.status == 'closed' || signal.data.status == 'failed') {
                                this.gateway.stop();
                            } else if (signal.data.status == 'preparing') {
                                this.gateway.pause();
                            } else if (signal.data.status == 'connected') {
                                this.gateway.resume();
                            }
                            break;
                        }
                    }
                }
            })(this)
        );
    }

    async start() {
        if (!this.stopped) {
            console.log('cmd gateways started already, cannot start again');
            return;
        }
        // console.log('Started cmd gateways');
        this.stopped = false;
        this.allowEmission = true;
        this.doStart();
    }

    private doStart() {
        const fn = async () => {
            while (!this.stopped) {
                if (!this.allowEmission) {
                    await delayPromise(10);
                    continue;
                }
                // apply all the command
                const cmd = this.cmdQueue.shift();
                if (!cmd) {
                    await delayPromise(10);
                    continue;
                }
                this.proceedReceivedCmd(cmd);
            }
        };
        fn();
    }

    async pause() {
        // console.log('Paused cmd gateways');
        this.allowEmission = false;
    }

    async resume() {
        // console.log('Resume cmd gateways');
        this.allowEmission = true;
    }

    async stop() {
        // console.log('Stopped cmd gateways');
        this.allowEmission = false;
        this.stopped = true;
    }

    registerChannel(channelCode: number): CmdChannel {
        if (!this.channels[channelCode]) this.channels[channelCode] = new DefaultCmdChannel(channelCode, this);

        return this.channels[channelCode];
    }

    get userId() {
        return this.synConf.userId;
    }

    get roomId() {
        return this.synConf.roomId;
    }

    /**
     * Register the viewport to gateway when its readies to receive cmd
     * The cmd that received before the viewport ready will be cached
     * When the viewport is registered, all cached cmd will be sent to editor through channel
     * @param vpId
     */
    registerViewport(vpId: string) {
        if (!this.viewportCmdQueue.has(vpId)) this.viewportCmdQueue.set(vpId, { ready: false, receiveQueue: [] });

        const vm = this.classroomCoord.getViewportManager(vpId);
        const vp = this.viewportCmdQueue.get(vpId);
        vp.ready = true;
        while (vp.receiveQueue.length > 0) {
            const cmd = vp.receiveQueue.shift();

            if (!cmd[1].meta.viewport) {
                cmd[1].meta.viewport = vm; // fill the missing ViewportManager
            }
            cmd[0].receive(cmd[1]);
        }
    }

    unregisterViewport(vpId: string) {
        if (!this.viewportCmdQueue.has(vpId)) return;

        const vp = this.viewportCmdQueue.get(vpId);
        vp.ready = false;
        while (vp.receiveQueue.length > 0) {
            const cmd = vp.receiveQueue.shift();
            cmd[0].receive(cmd[1]);
        }

        this.viewportCmdQueue.delete(vpId);
    }

    sync(cmd: Cmd<any>) {
        try {
            // serialize meta data
            const msgMeta = new CmdMetaProto()
                .setChannelCode(cmd.meta.channelCode)
                .setCmdType(cmd.meta.cmdType)
                .setTargetId(cmd.meta.targetId);

            const vm = cmd.meta.viewport as ViewportManager; // we know that this command gateway is used within classroom context
            const cid: string = vm.id;
            if (!this.idCache[cid]) this.idCache[cid] = strIdToBytes(cid);

            if (cmd.meta.versionable != undefined) msgMeta.setVersionable(cmd.meta.versionable);
            if (cmd.meta.sequence != undefined) msgMeta.setSequence(cmd.meta.sequence);

            const metaArrBuf = msgMeta.serializeBinary();
            const stateArrBuf = cmd.serialize();
            const final = new Uint8Array(
                this.idCache[cid].byteLength + 1 + metaArrBuf.byteLength + 1 + stateArrBuf.byteLength
            );

            let index = 0;
            final.set([this.idCache[cid].byteLength], index++);
            final.set(this.idCache[cid], index);
            index += this.idCache[cid].byteLength;
            final.set([metaArrBuf.byteLength], index++);
            final.set(metaArrBuf, index);
            index += metaArrBuf.byteLength;
            final.set(stateArrBuf, index);

            if (cmd.meta.sequence || cmd.meta.reliable) this.cmdGatewayConn.sendReliable(final);
            else this.cmdGatewayConn.sendUnreliable(final);
        } catch (err) {
            console.error('sync cmd failed... ', err);
            console.log('ERROR CMD', cmd);
            throw err;
        }
    }

    receive(cmd: Uint8Array) {
        if (!this.stopped && this.allowEmission && this.cmdQueue.length == 0) {
            this.proceedReceivedCmd(cmd);
        } else {
            this.cmdQueue.push(cmd);
        }
    }

    proceedReceivedCmd(cmd: Uint8Array) {
        let index = 0;
        const cidLength = cmd[index++];
        const cidBytes = cmd.slice(index, index + cidLength);

        const cid = bytesToStrId(cidBytes);

        index += cidLength;
        const metaLength = cmd[index++];
        const metaArrBuf = cmd.slice(index, index + metaLength);
        index += metaLength;
        const stateData = cmd.slice(index);

        const metaProto = CmdMetaProto.deserializeBinary(metaArrBuf);

        const vm = this.classroomCoord.getViewportManager(cid);

        const cmdMeta: CmdMeta = {
            channelCode: metaProto.getChannelCode(),
            targetId: metaProto.getTargetId(),
            cmdType: metaProto.getCmdType(),
            origin: CmdOriginType.remote,
            reliable: true, // not very important to the receiving end
            notSync: true, // no need further syncing
            sequence: metaProto.getSequence(),
            versionable: metaProto.getVersionable(),
            viewport: vm,
        };

        const channel = this.channels[cmdMeta.channelCode];

        const viewport = this.viewportCmdQueue.get(cid);
        if (viewport && viewport.ready && viewport.receiveQueue.length <= 0) {
            channel?.receive(channel.deserialize(cmdMeta, stateData));
        } else {
            if (!viewport)
                this.viewportCmdQueue.set(cid, {
                    ready: false,
                    receiveQueue: [],
                });
            if (channel)
                this.viewportCmdQueue.get(cid).receiveQueue.push([channel, channel.deserialize(cmdMeta, stateData)]);
        }
    }
}
