# Freedrawing Editor

A comprehensive drawing editor package for the viclass education platform, providing real-time collaborative drawing capabilities with support for various drawing tools including pen, eraser, and geometric shapes.

## Overview

The Freedrawing Editor is a micro-frontend component built with Angular and TypeScript that enables users to create, edit, and collaborate on drawings in real-time. It integrates seamlessly with the viclass platform's micro-service architecture and supports both collaborative classroom environments and single-user embed scenarios.

### Key Features

- **Real-time Collaboration**: Multi-user drawing with live synchronization
- **Multiple Drawing Tools**: Pen, eraser, and geometric shapes (line, rectangle, triangle, hexagon, oval)
- **Layer Management**: Hierarchical organization of drawing objects
- **Undo/Redo**: Complete history management with command pattern
- **Canvas Rendering**: High-performance rendering using PixiJS
- **Responsive UI**: Floating toolbar with tool selection and configuration
- **Backend Integration**: Seamless communication with freedrawing backend services

## Architecture

### Core Components

#### 1. FreedrawingEditor
The main editor class that orchestrates all drawing operations:
- Manages viewport and canvas initialization
- Handles document loading and persistence
- Coordinates between tools, commands, and rendering layers
- Provides public API for external integration

#### 2. Command System
Implements the Command Pattern for all drawing operations:
- **Commands**: `InsertDocCmd`, `InsertLayerCmd`, `InsertShapeObjCmd`, `UpdateShapeObjCmd`, `InsertPartialPencilObjCmd`, `InsertPartialEraserObjCmd`, `StartPreviewCmd`, `UpdatePreviewCmd`, `EndPreviewCmd`, `RemoveFreedrawingObjCmd`, `RemoveLayerCmd`
- **Command Processor**: `FreedrawingCmdProcessor` handles command execution and state management
- **History Management**: Enables undo/redo functionality

#### 3. Drawing Tools
Specialized tools for different drawing operations:
- **PenTool**: Freehand drawing with stroke optimization
- **EraserTool**: Object removal with preview functionality
- **ShapeTools**: Geometric shapes (LineTool, RectTool, TriangleTool, HexagonTool, OvalTool)
- **FloatingUITool**: Toolbar interaction and positioning

#### 4. Object Controllers
Manage rendering and interaction for different object types:
- **FreedrawingObjCtrl**: Base controller for all drawing objects
- **PenObjCtrl**: Handles pen stroke rendering and updates
- **EraserObjCtrl**: Manages eraser object visualization
- **ShapeObjCtrl**: Base for geometric shape controllers with specialized implementations

#### 5. Rendering System
- **FreedrawingLayerRenderer**: Manages object rendering on canvas layers
- **PixiCanvasLayer**: PixiJS-based rendering for high performance
- **UnboundedPixiCanvasLayer**: Infinite canvas support

### Data Models

#### Core Objects
```typescript
// Document structure
FreedrawingDoc {
  id: number
  layers: FreedrawingLayer[]
}

// Layer organization
FreedrawingLayer {
  id: number
  objects: FreedrawingObj[]
}

// Drawing objects
FreedrawingObj (base)
├── PenObj (freehand strokes)
├── ShapeObj (geometric shapes)
└── EraserObj (eraser marks)
```

#### Tool States
- **CommonToolState**: Shared state for pen and shape tools
- **EraserToolState**: Specialized state for eraser operations
- **FloatingUIToolState**: UI interaction state

## Interaction Flow

### Drawing Object Creation Flow

1. **Tool Selection**: User selects a drawing tool from the floating toolbar
2. **Pointer Down**: Tool creates a preview object and starts command sequence
3. **Pointer Move**: Tool updates preview object with real-time feedback
4. **Command Batching**: Multiple update commands are batched for performance
5. **Pointer Up**: Tool finalizes the object and sends insert command
6. **Rendering**: Object controller renders the final object on canvas
7. **Synchronization**: Commands are sent to backend for persistence and collaboration

### Command Processing Flow

```
User Input → Tool → Command → CmdProcessor → Backend → Peers
              ↓
            Preview → ObjectController → Renderer → Canvas
```

### Rendering Pipeline

1. **Object Creation**: Tools create drawing objects with geometric data
2. **Controller Assignment**: Objects are assigned appropriate controllers
3. **Layer Management**: Objects are organized in hierarchical layers
4. **PixiJS Rendering**: Controllers use PixiJS for high-performance canvas rendering
5. **Viewport Management**: Rendering adapts to zoom, pan, and boundary changes

### Preview System

For real-time feedback during drawing:
1. **Preview Creation**: Tools create temporary preview objects
2. **Real-time Updates**: Preview objects update as user moves pointer
3. **Command Batching**: Update commands are batched to reduce network traffic
4. **Preview Finalization**: Preview objects are converted to permanent objects
5. **Cleanup**: Preview objects are removed after finalization

## Performance Optimizations

- **Command Batching**: Reduces network overhead for continuous operations
- **Stroke Optimization**: Intelligent point reduction for smooth curves
- **Preview Throttling**: Optimized update frequency for real-time feedback
- **Layer Management**: Efficient object organization and rendering

This package provides a complete drawing solution that balances performance, collaboration, and user experience within the viclass education platform ecosystem.
