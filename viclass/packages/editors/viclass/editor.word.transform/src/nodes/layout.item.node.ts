import { DOMConversionMap, DOMConversionOutput, EditorConfig, LexicalNode, SerializedElementNode } from 'lexical';

import { addClassNamesToElement } from '@lexical/utils';
import { ElementNode } from 'lexical';

export type SerializedLayoutItemNode = SerializedElementNode;

function $convertLayoutItemElement(): DOMConversionOutput | null {
    return { node: $createLayoutItemNode() };
}

export class LayoutItemNode extends ElementNode {
    static override getType(): string {
        return 'layout-item';
    }

    static override clone(node: LayoutItemNode): LayoutItemNode {
        return new LayoutItemNode(node.__key);
    }

    override createDOM(config: EditorConfig): HTMLElement {
        const dom = document.createElement('div');
        dom.setAttribute('data-lexical-layout-item', 'true');
        if (typeof config.theme['layoutItem'] === 'string') {
            addClassNamesToElement(dom, config.theme['layoutItem']);
        }
        return dom;
    }

    override updateDOM(): boolean {
        return false;
    }

    static override importDOM(): DOMConversionMap | null {
        return {
            div: (domNode: HTMLElement) => {
                if (!domNode.hasAttribute('data-lexical-layout-item')) {
                    return null;
                }
                return {
                    conversion: $convertLayoutItemElement,
                    priority: 2,
                };
            },
        };
    }

    static override importJSON(serializedNode: SerializedLayoutItemNode): LayoutItemNode {
        return $createLayoutItemNode().updateFromJSON(serializedNode);
    }

    override isShadowRoot(): boolean {
        return true;
    }
}

export function $createLayoutItemNode(): LayoutItemNode {
    return new LayoutItemNode();
}

export function $isLayoutItemNode(node: LexicalNode | null | undefined): node is LayoutItemNode {
    return node instanceof LayoutItemNode;
}
