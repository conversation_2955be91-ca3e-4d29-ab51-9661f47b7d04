import {
    $createListItemNode,
    $isListItemNode,
    $isListNode,
    ListItemNode,
    ListNode,
    ListType,
    SerializedListNode,
} from '@lexical/list';
import {
    $applyNodeReplacement,
    DOMConversionMap,
    DOMConversionOutput,
    DOMExportOutput,
    EditorConfig,
    isHTMLElement,
    LexicalEditor,
    LexicalNode,
    LexicalUpdateJSON,
    NodeKey,
} from 'lexical';

type CustomSerializedListNode = SerializedListNode & {
    listStyleType: CustomListType;
};

export type CustomListType =
    | 'check'
    | 'number'
    | 'number-upercase'
    | 'number-lowercase'
    | 'number-roman'
    | 'bullet'
    | 'bullet-pinwheel'
    | 'bullet-star'
    | 'bullet-plus';

const CUSTOM_LIST_TYPE_TO_CLASS_NAME: Record<CustomListType, string> = {
    check: '',
    number: 'LexicalEditorTheme__list-style-type-number',
    'number-upercase': 'LexicalEditorTheme__list-style-type-upercase',
    'number-lowercase': 'LexicalEditorTheme__list-style-type-lowercase',
    'number-roman': 'LexicalEditorTheme__list-style-type-roman-number',
    bullet: 'LexicalEditorTheme__list-style-type-point',
    'bullet-pinwheel': 'LexicalEditorTheme__list-style-type-pinwheel',
    'bullet-star': 'LexicalEditorTheme__list-style-type-star',
    'bullet-plus': 'LexicalEditorTheme__list-style-type-plus',
};

/**
 * Custom list node with more types to override the original list node which contains only number, bullet and check
 */
export class CustomListNode extends ListNode {
    __listStyleType: CustomListType;

    constructor(customListType: CustomListType, start: number, key?: NodeKey) {
        const listType = customListType ? fromCustomListTypeToListType(customListType) : undefined;
        super(listType, start, key);
        this.__listStyleType = customListType;
    }

    static override getType(): string {
        return 'custom-list';
    }

    /**
     * Getter for the custom list style type
     */
    getListStyleType(): CustomListType {
        return this.getLatest().__listStyleType;
    }

    /**
     * @inheritdoc
     */
    static override clone(node: CustomListNode): CustomListNode {
        return new CustomListNode(node.__listStyleType, node.__start, node.__key);
    }

    /**
     * @inheritdoc
     */
    override createDOM(config: EditorConfig, _editor?: LexicalEditor): HTMLElement {
        const elem = super.createDOM(config, _editor);
        elem.classList.add(CUSTOM_LIST_TYPE_TO_CLASS_NAME[this.__listStyleType]);
        return elem;
    }

    /**
     *
     * Called by lexical to import the custom list node from HTML (usually from clipboard)
     * Here we override the original conversion map with higher priority.
     *
     * @inheritdoc
     */
    static override importDOM(): DOMConversionMap | null {
        return {
            ol: () => ({
                conversion: $convertCustomListNode,
                priority: 1,
            }),
            ul: () => ({
                conversion: $convertCustomListNode,
                priority: 1,
            }),
        };
    }

    /**
     * @inheritdoc
     */
    static override importJSON(serializedNode: CustomSerializedListNode): CustomListNode {
        const customListNode = new CustomListNode(serializedNode.listType, serializedNode.start);
        customListNode.__listStyleType = serializedNode.listStyleType;
        return customListNode;
    }

    /**
     * @inheritdoc
     */
    override exportJSON(): CustomSerializedListNode {
        return {
            ...super.exportJSON(),
            type: this.getType(),
            listStyleType: this.__listStyleType,
        };
    }

    /**
     * @inheritdoc
     */
    override exportDOM(editor: LexicalEditor): DOMExportOutput {
        const output = super.exportDOM(editor);
        if (isHTMLElement(output.element)) {
            output.element.setAttribute('__customListType', this.__listStyleType);
        }
        return output;
    }

    /**
     * @inheritdoc
     */
    override updateFromJSON(serializedNode: LexicalUpdateJSON<CustomSerializedListNode>): this {
        const node = super.updateFromJSON(serializedNode);
        node.__listStyleType = serializedNode.listStyleType;
        return node;
    }
}

/**
 * Convert our custom list type to the Lexical list type (number, bullet or check).
 * We need this because the original `ListNode` use those types to determine if the node
 * should be an `ol` or `ul` element
 */
function fromCustomListTypeToListType(customListType: CustomListType): ListType {
    if (customListType.startsWith('number')) return 'number' as ListType;
    else if (customListType.startsWith('bullet')) return 'bullet' as ListType;
    else return customListType as ListType;
}

/**
 * --- Below functions is extracted from Lexical with some modifications ---
 */

/**
 * Wraps a node into a ListItemNode.
 * @param node - The node to be wrapped into a ListItemNode
 * @returns The ListItemNode which the passed node is wrapped in.
 */
export function $wrapInListItem(node: LexicalNode): ListItemNode {
    const listItemWrapper = $createListItemNode();
    return listItemWrapper.append(node);
}

/*
 * This function normalizes the children of a ListNode after the conversion from HTML,
 * ensuring that they are all ListItemNodes and contain either a single nested ListNode
 * or some other inline content.
 */
function $normalizeChildren(nodes: Array<LexicalNode>): Array<ListItemNode> {
    const normalizedListItems: Array<ListItemNode> = [];
    for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i];
        if ($isListItemNode(node)) {
            normalizedListItems.push(node);
            const children = node.getChildren();
            if (children.length > 1) {
                children.forEach(child => {
                    if ($isListNode(child)) {
                        normalizedListItems.push($wrapInListItem(child));
                    }
                });
            }
        } else {
            normalizedListItems.push($wrapInListItem(node));
        }
    }
    return normalizedListItems;
}

/**
 * Checks if a DOM node is a checklist. Can be from lexical, github or googledoc checklist
 */
function isDomChecklist(domNode: HTMLElement) {
    if (
        domNode.getAttribute('__lexicallisttype') === 'check' ||
        // is github checklist
        domNode.classList.contains('contains-task-list')
    ) {
        return true;
    }
    // if children are checklist items, the node is a checklist ul. Applicable for googledoc checklist pasting.
    for (const child of domNode.childNodes) {
        if (isHTMLElement(child) && child.hasAttribute('aria-checked')) {
            return true;
        }
    }
    return false;
}

/**
 * Helper function to convert an HTML list node to a Lexical list node.
 * To be used in the importDOM function of our custom list node
 */
function $convertCustomListNode(domNode: HTMLElement): DOMConversionOutput {
    const nodeName = domNode.nodeName.toLowerCase();
    const customListType = domNode.getAttribute('__customListType') as CustomListType;
    let node = null;
    if (nodeName === 'ol') {
        // @ts-ignore
        const start = domNode.start;
        node = $createCustomListNode('number', start);
    } else if (nodeName === 'ul') {
        if (isDomChecklist(domNode)) {
            node = $createCustomListNode('check');
        } else {
            node = $createCustomListNode('bullet');
        }
    }
    node.__listStyleType = customListType;

    return {
        after: $normalizeChildren,
        node,
    };
}

/**
 * Helper function to create a custom list node
 */
export function $createCustomListNode(listType: CustomListType = 'number', start = 1): CustomListNode {
    return $applyNodeReplacement(new CustomListNode(listType, start));
}

/**
 * Helper function to check if a node is a custom list node
 */
export function $isCustomListNode(node: ListNode | null): node is CustomListNode {
    return node instanceof CustomListNode;
}
